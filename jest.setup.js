// Set up Jest environment

// Increase timeout for all tests
jest.setTimeout(30000);

// Mock Next.js modules
jest.mock('next/headers', () => ({
  headers: () => new Map(),
}));

jest.mock('next-auth', () => ({
  getServerSession: jest.fn(() => Promise.resolve(null)),
}));

// Global beforeAll
beforeAll(() => {
  // Add any global setup here
});

// Global afterAll
afterAll(() => {
  // Add any global teardown here
});

// Global beforeEach
beforeEach(() => {
  // Reset all mocks before each test
  jest.clearAllMocks();
});

// Add custom matchers if needed
expect.extend({
  // Add custom matchers here
});
