[2025-07-09 14:17:32] === Prisma Migration Script ===
[2025-07-09 14:17:32] Checking if the database is running...
[2025-07-09 14:17:33] PostgreSQL container is not running. Starting it...
[2025-07-09 14:17:33] EXECUTING: docker compose up -d db
$ docker compose up -d db
unable to get image 'postgres:16': permission denied while trying to connect to the Docker daemon socket at unix:///var/run/docker.sock: Get "http://%2Fvar%2Frun%2Fdocker.sock/v1.51/images/postgres:16/json": dial unix /var/run/docker.sock: connect: permission denied
[2025-07-09 14:17:33] ERROR: Starting database container failed (exit code: 1)
[2025-07-09 14:17:33] Check the log file for details: logs/prisma_migrate_20250709_141732.log
[2025-07-09 14:17:33] Waiting for PostgreSQL to be ready...
