[2025-05-15 15:37:08] Starting deployment script
[2025-05-15 15:37:08] SECTION: CARBONX DEPLOYMENT SCRIPT
[2025-05-15 15:37:08] INFO: This script will deploy the Carbonx application to your chosen environment.
[2025-05-15 15:37:08] INFO: Log file: logs/deploy_20250515_153708.log
[2025-05-15 15:37:08] WARNING: You are running this script with sudo/root privileges.
[2025-05-15 15:37:08] INFO: For better compatibility, it's recommended to run without sudo:
[2025-05-15 15:37:08] OUTPUT:   ./scripts/carbonx-deploy.sh
[2025-05-15 15:37:08] INFO: Only Docker commands will be executed with sudo as needed.
[2025-05-15 15:37:08] INFO: Press Enter to continue anyway, or Ctrl+C to abort.
[2025-05-15 15:37:09] INFO: Checking Docker installation...
[2025-05-15 15:37:10] WARNING: Docker requires sudo privileges. Using sudo for Docker commands.
[2025-05-15 15:37:10] SUCCESS: Docker is installed and running
[2025-05-15 15:37:10] INFO: Checking Docker Compose installation...
[2025-05-15 15:37:10] SUCCESS: Docker Compose is installed and working
[2025-05-15 15:37:10] INFO: Checking pnpm installation...
[2025-05-15 15:37:10] INFO: Running with sudo/root privileges. Trying to find pnpm in the system...
[2025-05-15 15:37:10] SUCCESS: pnpm found at /home/<USER>/.local/share/pnpm/pnpm
[2025-05-15 15:37:10] INFO: Which environment would you like to deploy to?
[2025-05-15 15:37:11] SECTION: 🚀 DEPLOYING TO DEVELOPMENT ENVIRONMENT
[2025-05-15 15:37:11] INFO: Checking .env file...
[2025-05-15 15:37:11] SUCCESS: .env file is ready
[2025-05-15 15:37:11] STEP 1: Starting PostgreSQL container
[2025-05-15 15:37:11] EXECUTING: ./scripts/start-db.sh
[0;34mStarting PostgreSQL container for development...[0m
 Container carbonx-db-1  Running
[0;34mWaiting for PostgreSQL to be ready...[0m
/var/run/postgresql:5432 - accepting connections
[0;32mPostgreSQL is ready![0m
[0;34mConnection details:[0m
  Host: localhost
  Port: 5433 (mapped from container port 5432)
  User: postgres
  Password: postgres
  Database: carbon_exchange

[0;34mTo connect using psql:[0m
  sudo docker compose exec db psql -U postgres -d carbon_exchange

[0;34mTo stop the database:[0m
  sudo docker compose down db
[2025-05-15 15:37:12] SUCCESS: Starting PostgreSQL container completed successfully
[2025-05-15 15:37:12] STEP 2: Setting up the database
[2025-05-15 15:37:12] INFO: Generating Prisma client...
[2025-05-15 15:37:12] EXECUTING: /home/<USER>/.local/share/pnpm/pnpm exec prisma generate
Environment variables loaded from .env
Prisma schema loaded from prisma/schema.prisma
Warning: You did not specify an output path for your `generator` in schema.prisma. This behavior is deprecated and will no longer be supported in Prisma 7.0.0. To learn more visit https://pris.ly/cli/output-path

✔ Generated Prisma Client (v6.7.0) to ./node_modules/@prisma/client in 753ms

Start by importing your Prisma Client (See: https://pris.ly/d/importing-client)

Tip: Want to turn off tips and other hints? https://pris.ly/tip-4-nohints

[2025-05-15 15:37:18] SUCCESS: Generating Prisma client completed successfully
[2025-05-15 15:37:18] INFO: Running database migrations...
[2025-05-15 15:37:18] INFO: Fixing database constraints before migration...
[2025-05-15 15:37:18] EXECUTING: ./scripts/fix-db-constraints.sh
[0;34mFixing database constraints...[0m
[0;34mExecuting SQL fix...[0m
NOTICE:  Neither constraint exists, no action needed
DO
[0;32mDatabase constraints fixed successfully![0m
[0;34mYou can now try running the deployment script again.[0m
[2025-05-15 15:37:18] SUCCESS: Fixing database constraints completed successfully
[2025-05-15 15:37:18] INFO: Checking if migration reset is needed...
[2025-05-15 15:37:18] EXECUTING: ./scripts/reset-migration.sh
[0;34mResetting migration state in the database...[0m
[0;34mExecuting SQL reset...[0m
BEGIN
DO
COMMIT
NOTICE:  _prisma_migrations table does not exist
[0;32mMigration state reset successfully![0m
[0;34mNow try running the deployment script again:[0m
  ./scripts/carbonx-deploy.sh
[2025-05-15 15:37:18] SUCCESS: Resetting migration state completed successfully
[2025-05-15 15:37:18] INFO: Running migrations with --skip-generate...
[2025-05-15 15:37:18] EXECUTING: /home/<USER>/.local/share/pnpm/pnpm exec prisma migrate dev --skip-generate --name safe_migration
Environment variables loaded from .env
Prisma schema loaded from prisma/schema.prisma
Datasource "db": PostgreSQL database "carbon_exchange", schema "public" at "localhost:5432"

Drift detected: Your database schema is not in sync with your migration history.

The following is a summary of the differences between the expected database schema given your migrations files, and the actual schema of the database.

It should be understood as the set of changes to get from the expected schema to the actual schema.

If you are running this the first time on an existing database, please make sure to read this documentation page:
https://www.prisma.io/docs/guides/database/developing-with-prisma-migrate/troubleshooting-development

[+] Added enums
  - AccessLevel
  - AlertStatus
  - AssetType
  - AuditLogType
  - AuditStatus
  - BillingStatus
  - BillingType
  - BridgeStatus
  - CarbonCreditDocumentType
  - CarbonCreditStatus
  - ComparisonType
  - ComplianceCheckResult
  - ComplianceCheckType
  - ComplianceDocumentType
  - ComplianceRiskLevel
  - ComplianceStatus
  - DocumentStatus
  - DocumentType
  - FinancialMetricType
  - FinancialReportType
  - InvitationStatus
  - KycLevel
  - ListingVisibility
  - MarketplaceStatus
  - MetricStatus
  - NotificationPriority
  - NotificationType
  - OrderStatus
  - OrderType
  - OrganizationSize
  - OrganizationStatus
  - PaymentMethodStatus
  - PaymentMethodType
  - PriceAlertDirection
  - PricingStrategy
  - ProjectDocumentType
  - ProjectStatus
  - ProjectType
  - ReportStatus
  - RequestStatus
  - SubscriptionPlan
  - SubscriptionStatus
  - TeamRoleType
  - TokenStandard
  - TokenizationMethod
  - TokenizationStatus
  - TransactionCategory
  - TransactionStatus
  - TransactionType
  - UserRole
  - ValuationMethod
  - VerificationStatus
  - WalletAccessLevel
  - WalletRecoveryType
  - WalletType

[+] Added tables
  - AmlAlert
  - AmlCheck
  - AmlRule
  - AssetValuation
  - AuditLog
  - BillingRecord
  - BridgeTransaction
  - CarbonCredit
  - CarbonCreditDocument
  - CarbonCreditPrice
  - CarbonCreditVerification
  - ComplianceCheck
  - ComplianceDocument
  - ComplianceReport
  - CustomRole
  - Department
  - Division
  - Document
  - FinancialMetric
  - FinancialReport
  - GasSetting
  - Invitation
  - KycVerification
  - KycVerificationHistory
  - MarketplaceListing
  - MarketplaceWatchlist
  - NFT
  - Notification
  - NotificationPreference
  - OnboardingState
  - Order
  - Organization
  - OrganizationDraft
  - OrganizationFeeHistory
  - OrganizationRbacSettings
  - PasswordResetToken
  - PaymentMethod
  - PeriodComparison
  - Permission
  - PermissionGrant
  - PermissionRequest
  - PermissionUsageLog
  - PlatformSettings
  - Project
  - ProjectDocument
  - ProjectFinancialMetric
  - ProjectVerification
  - ResourcePermission
  - ResourceScope
  - Retirement
  - RolePermission
  - Subscription
  - TaxReport
  - Team
  - TeamMember
  - TeamRole
  - TemporaryPermission
  - Token
  - Tokenization
  - TokenizationStep
  - Transaction
  - TransactionAudit
  - User
  - UserCustomRole
  - VerificationToken
  - Wallet
  - WalletAccessControl
  - WalletAuditLog
  - WalletGuardian
  - WalletRecovery
  - WalletRecoveryApproval
  - WalletSecuritySetting
  - WatchlistItem

[*] Changed the `AmlAlert` table
  [+] Added foreign key on columns (amlCheckId)

[*] Changed the `AmlCheck` table
  [+] Added unique index on columns (organizationId)
  [+] Added unique index on columns (userId)
  [+] Added foreign key on columns (organizationId)
  [+] Added foreign key on columns (userId)

[*] Changed the `AmlRule` table
  [+] Added foreign key on columns (amlCheckId)

[*] Changed the `AssetValuation` table
  [+] Added foreign key on columns (carbonCreditId)
  [+] Added foreign key on columns (transactionId)

[*] Changed the `AuditLog` table
  [+] Added foreign key on columns (organizationId)
  [+] Added foreign key on columns (userId)

[*] Changed the `BillingRecord` table
  [+] Added foreign key on columns (organizationId)
  [+] Added foreign key on columns (paymentMethodId)

[*] Changed the `BridgeTransaction` table
  [+] Added foreign key on columns (sourceWalletId)

[*] Changed the `CarbonCredit` table
  [+] Added foreign key on columns (organizationId)
  [+] Added foreign key on columns (projectId)
  [+] Added foreign key on columns (userId)

[*] Changed the `CarbonCreditDocument` table
  [+] Added foreign key on columns (carbonCreditId)

[*] Changed the `CarbonCreditPrice` table
  [+] Added foreign key on columns (carbonCreditId)

[*] Changed the `CarbonCreditVerification` table
  [+] Added foreign key on columns (carbonCreditId)

[*] Changed the `ComplianceCheck` table
  [+] Added foreign key on columns (organizationId)
  [+] Added foreign key on columns (userId)

[*] Changed the `ComplianceDocument` table
  [+] Added foreign key on columns (carbonCreditId)
  [+] Added foreign key on columns (carbonCreditId)
  [+] Added foreign key on columns (kycVerificationId)
  [+] Added foreign key on columns (organizationId)
  [+] Added foreign key on columns (projectId)
  [+] Added foreign key on columns (userId)

[*] Changed the `ComplianceReport` table
  [+] Added foreign key on columns (organizationId)
  [+] Added foreign key on columns (userId)

[*] Changed the `CustomRole` table
  [+] Added foreign key on columns (organizationId)
  [+] Added foreign key on columns (parentRoleId)

[*] Changed the `Department` table
  [+] Added foreign key on columns (organizationId)
  [+] Added foreign key on columns (parentId)

[*] Changed the `Division` table
  [+] Added foreign key on columns (departmentId)
  [+] Added foreign key on columns (organizationId)

[*] Changed the `Document` table
  [+] Added foreign key on columns (organizationId)

[*] Changed the `FinancialMetric` table
  [+] Added foreign key on columns (organizationId)

[*] Changed the `FinancialReport` table
  [+] Added foreign key on columns (organizationId)

[*] Changed the `GasSetting` table
  [+] Added unique index on columns (walletId)
  [+] Added foreign key on columns (walletId)

[*] Changed the `Invitation` table
  [+] Added unique index on columns (token)
  [+] Added foreign key on columns (organizationId)

[*] Changed the `KycVerification` table
  [+] Added unique index on columns (organizationId)
  [+] Added unique index on columns (userId)
  [+] Added foreign key on columns (organizationId)
  [+] Added foreign key on columns (userId)

[*] Changed the `KycVerificationHistory` table
  [+] Added foreign key on columns (kycVerificationId)

[*] Changed the `MarketplaceListing` table
  [+] Added foreign key on columns (carbonCreditId)
  [+] Added foreign key on columns (organizationId)
  [+] Added foreign key on columns (userId)

[*] Changed the `MarketplaceWatchlist` table
  [+] Added foreign key on columns (organizationId)
  [+] Added foreign key on columns (userId)

[*] Changed the `NFT` table
  [+] Added unique index on columns (contractAddress, tokenId, walletId)
  [+] Added foreign key on columns (walletId)

[*] Changed the `Notification` table
  [+] Added foreign key on columns (organizationId)
  [+] Added foreign key on columns (userId)

[*] Changed the `NotificationPreference` table
  [+] Added unique index on columns (userId)
  [+] Added foreign key on columns (userId)

[*] Changed the `OnboardingState` table
  [+] Added index on columns (userId)
  [+] Added unique index on columns (userId)
  [+] Added foreign key on columns (userId)

[*] Changed the `Order` table
  [+] Added foreign key on columns (buyerId)
  [+] Added foreign key on columns (carbonCreditId)
  [+] Added foreign key on columns (marketplaceListingId)
  [+] Added foreign key on columns (sellerId)

[*] Changed the `OrganizationDraft` table
  [+] Added index on columns (userId)
  [+] Added unique index on columns (userId)
  [+] Added foreign key on columns (userId)

[*] Changed the `OrganizationFeeHistory` table
  [+] Added foreign key on columns (organizationId)

[*] Changed the `OrganizationRbacSettings` table
  [+] Added unique index on columns (organizationId)
  [+] Added foreign key on columns (organizationId)

[*] Changed the `PasswordResetToken` table
  [+] Added unique index on columns (token)
  [+] Added foreign key on columns (userId)

[*] Changed the `PaymentMethod` table
  [+] Added foreign key on columns (organizationId)

[*] Changed the `PeriodComparison` table
  [+] Added foreign key on columns (organizationId)

[*] Changed the `Permission` table
  [+] Added unique index on columns (name)

[*] Changed the `PermissionGrant` table
  [+] Added unique index on columns (userId, permissionId)
  [+] Added foreign key on columns (permissionId)
  [+] Added foreign key on columns (userId)

[*] Changed the `PermissionRequest` table
  [+] Added foreign key on columns (approverId)
  [+] Added foreign key on columns (permissionId)
  [+] Added foreign key on columns (userId)

[*] Changed the `Project` table
  [+] Added foreign key on columns (organizationId)

[*] Changed the `ProjectDocument` table
  [+] Added foreign key on columns (projectId)

[*] Changed the `ProjectFinancialMetric` table
  [+] Added foreign key on columns (projectId)

[*] Changed the `ProjectVerification` table
  [+] Added foreign key on columns (projectId)

[*] Changed the `ResourcePermission` table
  [+] Added unique index on columns (permissionId, resourceType, resourceId)
  [+] Added foreign key on columns (permissionId)

[*] Changed the `ResourceScope` table
  [+] Added unique index on columns (teamId, resourceType, resourceId)
  [+] Added foreign key on columns (teamId)

[*] Changed the `Retirement` table
  [+] Added foreign key on columns (carbonCreditId)
  [+] Added foreign key on columns (organizationId)
  [+] Added foreign key on columns (userId)

[*] Changed the `RolePermission` table
  [+] Added unique index on columns (roleId, permissionId)
  [+] Added foreign key on columns (permissionId)
  [+] Added foreign key on columns (roleId)

[*] Changed the `Subscription` table
  [+] Added unique index on columns (organizationId)
  [+] Added foreign key on columns (organizationId)

[*] Changed the `TaxReport` table
  [+] Added foreign key on columns (organizationId)
  [+] Added foreign key on columns (userId)

[*] Changed the `Team` table
  [+] Added foreign key on columns (organizationId)

[*] Changed the `TeamMember` table
  [+] Added unique index on columns (teamId, userId)
  [+] Added foreign key on columns (teamId)
  [+] Added foreign key on columns (teamRoleId)
  [+] Added foreign key on columns (userId)

[*] Changed the `TeamRole` table
  [+] Added foreign key on columns (teamId)

[*] Changed the `TemporaryPermission` table
  [+] Added unique index on columns (userId, permissionId, resourceType, resourceId)
  [+] Added foreign key on columns (permissionId)
  [+] Added foreign key on columns (userId)

[*] Changed the `Token` table
  [+] Added unique index on columns (contractAddress, walletId)
  [+] Added foreign key on columns (walletId)

[*] Changed the `Tokenization` table
  [+] Added foreign key on columns (carbonCreditId)
  [+] Added foreign key on columns (organizationId)
  [+] Added foreign key on columns (userId)
  [+] Added foreign key on columns (walletId)

[*] Changed the `TokenizationStep` table
  [+] Added foreign key on columns (tokenizationId)

[*] Changed the `Transaction` table
  [+] Added foreign key on columns (assetValuationId)
  [+] Added foreign key on columns (orderId)
  [+] Added foreign key on columns (transactionAuditId)
  [+] Added foreign key on columns (assetValuationId)
  [+] Added foreign key on columns (transactionAuditId)
  [+] Added foreign key on columns (walletId)

[*] Changed the `TransactionAudit` table
  [+] Added foreign key on columns (transactionId)

[*] Changed the `User` table
  [+] Added unique index on columns (email)
  [+] Added foreign key on columns (departmentId)
  [+] Added foreign key on columns (divisionId)
  [+] Added foreign key on columns (organizationId)

[*] Changed the `UserCustomRole` table
  [+] Added unique index on columns (userId, roleId)
  [+] Added foreign key on columns (roleId)
  [+] Added foreign key on columns (userId)

[*] Changed the `VerificationToken` table
  [+] Added unique index on columns (token)
  [+] Added foreign key on columns (userId)

[*] Changed the `Wallet` table
  [+] Added unique index on columns (address, network, chainId)
  [+] Added foreign key on columns (organizationId)
  [+] Added foreign key on columns (projectId)
  [+] Added foreign key on columns (userId)

[*] Changed the `WalletAccessControl` table
  [+] Added unique index on columns (walletId, userId)
  [+] Added foreign key on columns (walletId)

[*] Changed the `WalletAuditLog` table
  [+] Added foreign key on columns (walletId)

[*] Changed the `WalletGuardian` table
  [+] Added unique index on columns (walletId, address)
  [+] Added foreign key on columns (walletId)

[*] Changed the `WalletRecovery` table
  [+] Added foreign key on columns (walletId)

[*] Changed the `WalletRecoveryApproval` table
  [+] Added unique index on columns (recoveryId, guardianId)
  [+] Added foreign key on columns (guardianId)
  [+] Added foreign key on columns (recoveryId)

[*] Changed the `WalletSecuritySetting` table
  [+] Added unique index on columns (walletId)
  [+] Added foreign key on columns (walletId)

[*] Changed the `WatchlistItem` table
  [+] Added foreign key on columns (listingId)
  [+] Added foreign key on columns (watchlistId)

We need to reset the "public" schema at "localhost:5432"

You may use prisma migrate reset to drop the development database.
All data will be lost.
[2025-05-15 15:37:21] ERROR: Running database migrations failed (exit code: 1)
[2025-05-15 15:37:21] INFO: Check the log file for details: logs/deploy_20250515_153708.log
[2025-05-15 15:37:21] WARNING: Migration failed. Trying direct schema push...
[2025-05-15 15:37:21] INFO: Using direct schema push to bypass migrations...
[2025-05-15 15:37:21] EXECUTING: ./scripts/direct-schema-push.sh
[0;31mpnpm not found. Please install pnpm or add it to your PATH.[0m
[2025-05-15 15:37:21] ERROR: Pushing schema directly failed (exit code: 1)
[2025-05-15 15:37:21] INFO: Check the log file for details: logs/deploy_20250515_153708.log
[2025-05-15 15:37:21] ERROR: Direct schema push failed. Please check your database and schema.
[2025-05-15 15:37:21] INFO: You may need to manually fix the database schema.
[2025-05-15 15:37:21] INFO: Try running: ./scripts/reset-database.sh
