# Module Consolidation Plan

## Overview

This document outlines the plan for consolidating the modules in the `carbon-exchange/src/lib` directory. The goal is to improve code organization, reduce redundancy, and enhance maintainability.

## Current Status

We have successfully created the new directory structure and implemented the new modules:

- ✅ Created directory structure
- ✅ Implemented analytics modules
- ✅ Implemented blockchain/gas modules
- ✅ Implemented notifications modules
- ✅ Implemented carbon credits modules
- ✅ Implemented audit modules
- ✅ Implemented payments modules
- ✅ Implemented orders modules
- ✅ Implemented marketplace modules
- ✅ Created compatibility layer for all modules

However, the old files still exist alongside the new ones, and imports in other files still reference the old modules.

## Next Steps

### 1. Update Imports in New Modules

Ensure all imports in the new modules reference the new module paths.

### 2. Identify Files Using Old Modules

Identify all files that import from the old modules:

```bash
grep -r "from '@/lib/analytics-service" carbon-exchange/src --include="*.ts" --include="*.tsx"
grep -r "from '@/lib/blockchain-client" carbon-exchange/src --include="*.ts" --include="*.tsx"
grep -r "from '@/lib/gas-estimation" carbon-exchange/src --include="*.ts" --include="*.tsx"
grep -r "from '@/lib/gas-optimizer" carbon-exchange/src --include="*.ts" --include="*.tsx"
grep -r "from '@/lib/notification-service" carbon-exchange/src --include="*.ts" --include="*.tsx"
grep -r "from '@/lib/carbon-credit-service" carbon-exchange/src --include="*.ts" --include="*.tsx"
```

### 3. Update Imports in Other Files

Update imports in all files to reference the new module paths:

- `@/lib/analytics-service` → `@/lib/analytics`
- `@/lib/blockchain-client` → `@/lib/blockchain/core/client`
- `@/lib/gas-estimation` → `@/lib/blockchain/gas`
- `@/lib/gas-optimizer` → `@/lib/blockchain/gas`
- `@/lib/notification-service` → `@/lib/notifications`
- `@/lib/carbon-credit-service` → `@/lib/carbon-credits`

### 4. Create Missing Modules

Identify any modules that are still needed but haven't been consolidated yet:

- ✅ Audit module
- ✅ Payments module
- ✅ Order matching engine
- ✅ Marketplace module

### 5. Test Functionality

Ensure all functionality works correctly with the new module structure:

- Run unit tests
- Test key features manually
- Check for any runtime errors

### 6. Remove Old Files

Once all imports have been updated and functionality has been tested, remove the old files:

```bash
rm carbon-exchange/src/lib/analytics-service.ts
rm carbon-exchange/src/lib/analytics.ts
rm carbon-exchange/src/lib/blockchain-client.ts
rm carbon-exchange/src/lib/blockchain-config.ts
rm carbon-exchange/src/lib/blockchain.ts
rm carbon-exchange/src/lib/gas-estimation.ts
rm carbon-exchange/src/lib/gas-optimizer.ts
rm carbon-exchange/src/lib/notification-service.ts
rm carbon-exchange/src/lib/notifications.ts
rm carbon-exchange/src/lib/carbon-credit-service.ts
rm carbon-exchange/src/lib/carbon-credit-verification.ts
```

### 7. Update Documentation

Update documentation to reflect the new module structure:

- Update README files
- Update API documentation
- Update developer guides

## Module Mapping

| Old Module | New Module |
|------------|------------|
| `analytics-service.ts` | `analytics/index.ts` |
| `analytics.ts` | `analytics/index.ts` |
| `blockchain-client.ts` | `blockchain/core/client.ts` |
| `blockchain-config.ts` | `blockchain/config/networks.ts` |
| `blockchain.ts` | `blockchain/index.ts` |
| `gas-estimation.ts` | `blockchain/gas/estimation.ts` |
| `gas-optimizer.ts` | `blockchain/gas/optimization.ts` |
| `notification-service.ts` | `notifications/index.ts` |
| `notifications.ts` | `notifications/index.ts` |
| `carbon-credit-service.ts` | `carbon-credits/service.ts` |
| `carbon-credit-verification.ts` | `carbon-credits/verification.ts` |

## Timeline

1. ✅ Update imports in new modules (1 day)
2. ✅ Identify files using old modules (1 day)
3. Update imports in other files (2-3 days)
4. ✅ Create missing modules (3-5 days)
5. Test functionality (2-3 days)
6. Remove old files (1 day)
7. ✅ Update documentation (1-2 days)

Total estimated time: 2-3 weeks

## Migration Scripts

To help with the migration process, we've created two scripts:

1. `scripts/migrate-imports.sh`: Helps identify files that still use old imports and provides guidance on how to update them.
2. `scripts/remove-old-modules.sh`: Removes old module files after all imports have been migrated.

These scripts should be run in order, with the removal script only being run after all imports have been updated.
