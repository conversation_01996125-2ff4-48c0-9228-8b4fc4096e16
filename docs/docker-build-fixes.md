# Docker Build Fixes

## 🐛 **Issue Resolved**

### **Error**: Docker build failing with standalone output not found
```
ERROR [runner 6/18] COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
failed to compute cache key: "/app/.next/standalone": not found
```

### **Root Cause**:
- Next.js config was using custom `distDir: '/tmp/.next-carbonx'` for development
- Docker build expected output in standard `.next/standalone` directory
- The `output: 'standalone'` was configured but output was in wrong location

---

## ✅ **Fixes Applied**

### **1. Fixed Next.js Configuration**
**File**: `dev-optimized.config.js`

**Before**:
```javascript
module.exports = {
  output: 'standalone',
  distDir: '/tmp/.next-carbonx', // ❌ Custom directory causing issues
  // ...
};
```

**After**:
```javascript
module.exports = {
  output: 'standalone',
  // ✅ Use standard .next directory for Docker compatibility
  // distDir: isDev ? '/tmp/.next-carbonx' : '.next', // Commented out
  // ...
};
```

### **2. Enhanced Dockerfile Build Verification**
**File**: `Dockerfile`

**Added build verification step**:
```dockerfile
# Verify standalone output exists and show build structure
RUN echo "Build completed. Checking output structure:" && \
    ls -la . && \
    echo "Contents of .next directory:" && \
    ls -la .next/ && \
    echo "Checking for standalone directory:" && \
    ls -la .next/standalone/ || echo "Standalone directory not found"
```

### **3. Created Docker Build Test Script**
**File**: `scripts/test-docker-build.sh`

**Features**:
- Tests Docker build process locally
- Validates image creation and container startup
- Provides detailed feedback on build success/failure
- Includes cleanup and optimization suggestions

---

## 🔧 **Next.js Standalone Output Configuration**

### **What is Standalone Output?**
Next.js standalone output creates a self-contained application that includes:
- All necessary files to run the application
- Node.js modules bundled together
- Optimized for Docker containers

### **Required Configuration**:
```javascript
// next.config.js
module.exports = {
  output: 'standalone', // ✅ Required for Docker builds
  // distDir: '.next',  // ✅ Use default directory
};
```

### **Generated Structure**:
```
.next/
├── standalone/          # ✅ Self-contained app
│   ├── server.js       # Entry point
│   ├── package.json    # Dependencies
│   └── ...
├── static/             # ✅ Static assets
└── ...
```

---

## 🐳 **Docker Build Process**

### **Build Stages**:

1. **Base Stage**: Set up Node.js environment
2. **Dependencies Stage**: Install packages
3. **Builder Stage**: Build the application
4. **Runner Stage**: Create production container

### **Key Docker Commands**:

```dockerfile
# Build with standalone output
RUN pnpm build

# Copy standalone app
COPY --from=builder /app/.next/standalone ./

# Copy static assets
COPY --from=builder /app/.next/static ./.next/static

# Copy public files
COPY --from=builder /app/public ./public
```

---

## 🚀 **How to Build and Test**

### **Option 1: Standard Docker Build**
```bash
docker build -t carbonx-app .
```

### **Option 2: Using Test Script**
```bash
./scripts/test-docker-build.sh
```

### **Option 3: Docker Compose**
```bash
docker-compose up --build
```

---

## 🔍 **Troubleshooting**

### **Common Issues and Solutions**:

#### **Issue**: `standalone` directory not found
**Solution**: Ensure `output: 'standalone'` is in Next.js config

#### **Issue**: Build takes too long
**Solution**: Use Docker BuildKit and layer caching
```bash
DOCKER_BUILDKIT=1 docker build -t carbonx-app .
```

#### **Issue**: Permission errors
**Solution**: Ensure proper user permissions in Dockerfile
```dockerfile
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
```

#### **Issue**: Missing static files
**Solution**: Verify static directory copy
```dockerfile
COPY --from=builder /app/.next/static ./.next/static
```

---

## 📋 **Build Verification Checklist**

Before deploying, verify:

- ✅ `output: 'standalone'` in Next.js config
- ✅ `.next/standalone/` directory exists after build
- ✅ `.next/static/` directory exists after build
- ✅ `public/` directory copied correctly
- ✅ Docker image builds without errors
- ✅ Container starts successfully
- ✅ Application responds on expected port

---

## 🎯 **Production Deployment**

### **Environment Variables**:
```bash
NODE_ENV=production
PORT=3000
DATABASE_URL=postgresql://...
NEXTAUTH_SECRET=...
```

### **Health Check**:
```dockerfile
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1
```

### **Resource Limits**:
```yaml
# docker-compose.yml
services:
  app:
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
```

---

## 🔧 **Development vs Production**

### **Development**:
- Fast rebuilds
- Hot reloading
- Source maps
- Detailed error messages

### **Production**:
- Optimized bundles
- Standalone output
- Minimal image size
- Security hardening

---

## 📊 **Build Optimization**

### **Image Size Optimization**:
- Multi-stage builds
- Alpine Linux base
- Minimal dependencies
- Layer caching

### **Build Speed Optimization**:
- Docker BuildKit
- Dependency caching
- Parallel builds
- Incremental builds

The Docker build should now work successfully with the standalone output configuration! 🐳
