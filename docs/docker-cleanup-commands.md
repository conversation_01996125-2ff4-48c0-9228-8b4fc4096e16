# Docker Cleanup Commands Reference

This document provides a comprehensive list of commands to stop, remove, and clean up Docker containers, images, volumes, and networks. Use these commands when you need to free up resources or start with a clean Docker environment.

## Basic Docker Commands

### View Docker Resources

```bash
# List all running containers
docker ps

# List all containers (including stopped ones)
docker ps -a

# List all images
docker images

# List all volumes
docker volume ls

# List all networks
docker network ls

# Show Docker disk usage
docker system df
```

## Container Management

### Stop Containers

```bash
# Stop a specific container
docker stop <container_id_or_name>

# Stop all running containers
docker stop $(docker ps -q)
```

### Remove Containers

```bash
# Remove a specific stopped container
docker rm <container_id_or_name>

# Force remove a running container
docker rm -f <container_id_or_name>

# Remove all stopped containers
docker container prune

# Remove all containers (including running ones)
docker rm -f $(docker ps -a -q)
```

## Image Management

### Remove Images

```bash
# Remove a specific image
docker rmi <image_id_or_name>

# Remove multiple images
docker rmi <image_id1> <image_id2>

# Remove all unused images (not associated with a container)
docker image prune -a

# Remove all images
docker rmi $(docker images -q)
```

## Volume Management

```bash
# Remove a specific volume
docker volume rm <volume_name>

# Remove all unused volumes
docker volume prune

# Remove all volumes (CAUTION: This will delete all data)
docker volume rm $(docker volume ls -q)
```

## Network Management

```bash
# Remove a specific network
docker network rm <network_name>

# Remove all unused networks
docker network prune
```

## Complete System Cleanup

```bash
# Remove all unused containers, networks, images (both dangling and unreferenced), and optionally, volumes
docker system prune -a

# Same as above but also removes volumes
docker system prune -a --volumes
```

## Docker Compose Commands

### Basic Operations

```bash
# Stop services
docker compose stop

# Start services
docker compose start

# Restart services
docker compose restart

# Stop and remove containers, networks
docker compose down

# Stop and remove containers, networks, and volumes
docker compose down -v

# Stop and remove containers, networks, and images
docker compose down --rmi all

# Stop and remove containers, networks, volumes, and images
docker compose down -v --rmi all
```

### Environment-Specific Commands

```bash
# For development environment
docker compose down -v

# For pre-production environment
docker compose -f docker-compose.preprod.yml down -v

# For production environment
docker compose -f docker-compose.prod.yml down -v
```

## Recommended Cleanup Sequences

### Quick Cleanup (Keep Images)

```bash
# Stop and remove all containers
docker compose down

# Remove unused volumes
docker volume prune
```

### Full Cleanup (Fresh Start)

```bash
# Stop and remove all containers, networks, and volumes for all environments
docker compose down -v
docker compose -f docker-compose.preprod.yml down -v
docker compose -f docker-compose.prod.yml down -v

# Remove all unused Docker objects (containers, networks, images, volumes)
docker system prune -a --volumes
```

### Targeted Cleanup for Carbonx Project

```bash
# Stop and remove specific services
docker compose stop app-dev db
docker compose rm app-dev db

# Remove specific volumes
docker volume rm carbonx_postgres_data carbonx_uploads_data

# Rebuild specific services
docker compose build app-dev
docker compose up -d app-dev
```

## Troubleshooting Commands

```bash
# View logs for a container
docker logs <container_id_or_name>

# View logs for a service in docker-compose
docker compose logs -f <service_name>

# Inspect a container
docker inspect <container_id_or_name>

# Check Docker daemon status
systemctl status docker

# Restart Docker daemon
sudo systemctl restart docker
```

## Safety Tips

1. **Always be careful with prune commands** - they remove unused resources which might be needed later
2. **Be extremely cautious with `-f` (force) flags** - they don't ask for confirmation
3. **Back up important data** before removing volumes
4. **Use specific IDs or names** when possible instead of removing everything
5. **Check what will be removed** with `docker system df` before pruning

## Common Issues and Solutions

### Cannot remove running container
Solution: Stop the container first or use force remove (`docker rm -f`)

### Image is being used by a container
Solution: Remove the container first, then the image

### Volume is in use
Solution: Remove the container using the volume first

### Permission denied
Solution: Use `sudo` or add your user to the docker group

### No space left on device
Solution: Run `docker system prune -a --volumes` to free up space
