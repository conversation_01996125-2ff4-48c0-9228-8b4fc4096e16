// dev-optimized.config.js
const isDev = process.env.NODE_ENV !== 'production';

module.exports = {
  // ✅ Enable standalone output for Docker production builds
  output: 'standalone',

  // ✅ Always use standard .next directory for Docker compatibility
  // distDir: isDev ? '/tmp/.next-carbonx' : '.next',

  // ✅ Disable ESLint during builds
  eslint: {
    ignoreDuringBuilds: true,
  },

  // ✅ Disable TypeScript type checking during builds
  typescript: {
    ignoreBuildErrors: true,
  },

  // ✅ Custom Webpack Tweaks
  webpack(config, { dev, isServer }) {
    if (dev && !isServer) {
      config.optimization.minimize = false;
    }

    return config;
  },
};
