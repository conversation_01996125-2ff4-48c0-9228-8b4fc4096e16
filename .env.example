# Database
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/carbon_exchange"
DATABASE_HOST="localhost"
DATABASE_PORT="5432"
DATABASE_USER="postgres"
DATABASE_PASSWORD="postgres"

# Next Auth
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret"

# Email (SMTP)
SMTP_HOST="smtp.example.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-smtp-password"
SMTP_FROM="<EMAIL>"

# Blockchain
ALCHEMY_API_KEY="your-alchemy-api-key"
ALCHEMY_NETWORK="eth-sepolia"
ALCHEMY_GAS_MANAGER_POLICY_ID="your-gas-manager-policy-id"
WALLET_ENCRYPTION_KEY="your-wallet-encryption-key"

# Blockchain networks (all test networks for development)
ETHEREUM_NETWORK="sepolia"
POLYGON_NETWORK="mumbai"
OPTIMISM_NETWORK="optimism-sepolia"
ARBITRUM_NETWORK="arbitrum-sepolia"
BASE_NETWORK="base-sepolia"

# Node environment
NODE_ENV="development"

# Storage
STORAGE_PROVIDER="local"
STORAGE_BASE_URL="http://localhost:3000"
STORAGE_ROOT_DIR="./public/uploads"
STORAGE_USE_RELATIVE_URLS="true"

# For S3 storage (when STORAGE_PROVIDER is set to "s3")
# S3_REGION="us-east-1"
# S3_BUCKET="your-s3-bucket"
# S3_ACCESS_KEY_ID="your-s3-access-key-id"
# S3_SECRET_ACCESS_KEY="your-s3-secret-access-key"

# KYC/AML Providers
KYC_PROVIDER_TYPE="MOCK" # Options: MOCK, ONFIDO, SUMSUB, JUMIO, TRULIOO, CUSTOM
# ONFIDO_API_KEY="your-onfido-api-key"
# ONFIDO_REGION="EU" # or "US", "CA", etc.
# SUMSUB_APP_TOKEN="your-sumsub-app-token"
# SUMSUB_SECRET_KEY="your-sumsub-secret-key"
# JUMIO_API_TOKEN="your-jumio-api-token"
# JUMIO_API_SECRET="your-jumio-api-secret"
# TRULIOO_API_KEY="your-trulioo-api-key"
# TRULIOO_USERNAME="your-trulioo-username"
# TRULIOO_PASSWORD="your-trulioo-password"
