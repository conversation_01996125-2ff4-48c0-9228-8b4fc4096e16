#!/usr/bin/env tsx

/**
 * Add monitoring data to a specific project
 */

import { db } from "../src/lib/db";
import { logger } from "../src/lib/logger";

async function addDataToSpecificProject() {
  try {
    const projectId = 'cmcvp4osk00025x50i3k31tx1';
    
    console.log(`Adding monitoring data to project: ${projectId}`);
    
    // Get the project details
    const project = await db.project.findUnique({
      where: { id: projectId },
      include: { organization: true }
    });
    
    if (!project) {
      console.log('Project not found');
      return;
    }
    
    console.log(`Project: ${project.name}`);
    console.log(`Organization: ${project.organization.name}`);
    
    // Get a user from the same organization
    const user = await db.user.findFirst({
      where: { organizationId: project.organizationId }
    });
    
    if (!user) {
      console.log('No user found for this organization');
      return;
    }
    
    console.log(`Adding data for user: ${user.name} (${user.email})`);
    
    // Add some sample unit logs
    for (let i = 0; i < 5; i++) {
      const logDate = new Date();
      logDate.setDate(logDate.getDate() - i * 7); // Weekly intervals
      
      const quantity = Math.floor(Math.random() * 1000) + 500;
      
      await db.unitLog.create({
        data: {
          logDate,
          frequency: 'WEEKLY',
          unitType: 'MWH',
          quantity,
          dataSource: 'MANUAL',
          notes: `Sample monitoring data entry ${i + 1} for ${project.name}`,
          metadata: {
            sampleData: true,
            generatedAt: new Date().toISOString(),
            projectType: project.type
          },
          projectId,
          loggedBy: user.id,
          verificationStatus: 'VERIFIED',
        }
      });
      
      console.log(`  Created entry ${i + 1}: ${quantity} MWH on ${logDate.toDateString()}`);
    }
    
    console.log('✅ Added 5 unit logs to project');
    
    // Verify the data was added
    const unitLogsCount = await db.unitLog.count({
      where: { projectId }
    });
    
    console.log(`📊 Total unit logs in project: ${unitLogsCount}`);
    
  } catch (error) {
    console.error('❌ Error adding data to project:', error);
    logger.error('Add data to project failed', error);
  } finally {
    await db.$disconnect();
  }
}

// Run the script
addDataToSpecificProject().catch(console.error);
