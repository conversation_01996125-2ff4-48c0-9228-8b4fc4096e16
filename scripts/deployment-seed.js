const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

/**
 * Deployment seed script
 * 
 * This script creates the minimum required data for the application to function properly:
 * 1. Creates the platform organization and admin user
 * 2. Creates a sample organization with an admin user
 */
async function main() {
  console.log('Starting deployment seed...');

  // Step 1: Create platform organization and admin user
  console.log('Creating platform organization and admin user...');
  
  // Check if admin user already exists
  let adminUser = await prisma.user.findFirst({
    where: { role: "ADMIN" }
  });

  let platformOrg;
  if (!adminUser) {
    // Create platform organization
    platformOrg = await prisma.organization.findFirst({
      where: { name: 'Carbon Exchange Platform' }
    });

    if (!platformOrg) {
      platformOrg = await prisma.organization.create({
        data: {
          name: 'Carbon Exchange Platform',
          description: 'The platform administrator organization',
          website: 'https://carbonexchange.example.com',
          status: "ACTIVE",
          verificationStatus: "VERIFIED",
          country: 'United States',
          industry: 'Technology',
          size: 'MEDIUM',
          foundedYear: 2020,
          primaryContact: 'Admin User',
          primaryContactEmail: '<EMAIL>',
          primaryContactPhone: '+18001234567',
          legalName: 'Carbon Exchange Platform Inc.',
          registrationNumber: 'US87654321',
          taxId: 'US123456789',
        }
      });
      console.log(`Created platform organization: ${platformOrg.name}`);
    } else {
      console.log(`Platform organization already exists: ${platformOrg.name}`);
    }

    // Create admin user
    adminUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Admin User',
        password: await bcrypt.hash('Admin123!', 10),
        role: "ADMIN",
        emailVerified: new Date(),
        organization: {
          connect: {
            id: platformOrg.id
          }
        }
      }
    });
    console.log(`Created admin user: ${adminUser.email} (password: Admin123!)`);
  } else {
    console.log(`Admin user already exists: ${adminUser.email}`);
    platformOrg = await prisma.organization.findUnique({
      where: { id: adminUser.organizationId || '' }
    });
  }

  // Step 2: Create a sample organization with admin user
  console.log('Creating sample organization and user...');
  
  // Check if sample organization exists
  let sampleOrg = await prisma.organization.findFirst({
    where: { name: 'GreenTech Solutions' }
  });

  let orgAdmin;
  if (!sampleOrg) {
    sampleOrg = await prisma.organization.create({
      data: {
        name: 'GreenTech Solutions',
        description: 'Innovative solutions for a sustainable future',
        website: 'https://greentech.example.com',
        status: "ACTIVE",
        verificationStatus: "VERIFIED",
        country: 'United States',
        industry: 'Renewable Energy',
        size: 'MEDIUM',
        foundedYear: 2010,
        primaryContact: 'John Smith',
        primaryContactEmail: '<EMAIL>',
        primaryContactPhone: '+1234567890',
        legalName: 'GreenTech Solutions Inc.',
        registrationNumber: 'US12345678',
        taxId: '98-7654321',
        address: '123 Green Street',
        city: 'San Francisco',
        state: 'CA',
        postalCode: '94105',
      }
    });
    console.log(`Created sample organization: ${sampleOrg.name}`);

    // Create organization admin
    orgAdmin = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'John Smith',
        password: await bcrypt.hash('Password123!', 10),
        role: "ORGANIZATION_ADMIN",
        emailVerified: new Date(),
        jobTitle: 'CEO',
        departmentName: 'Executive',
        phoneNumber: '+1234567890',
        bio: 'Founder and CEO with 15+ years in renewable energy',
        organization: {
          connect: {
            id: sampleOrg.id
          }
        }
      }
    });
    console.log(`Created organization admin: ${orgAdmin.email} (password: Password123!)`);
  } else {
    console.log(`Sample organization already exists: ${sampleOrg.name}`);
    orgAdmin = await prisma.user.findFirst({
      where: { 
        organizationId: sampleOrg.id,
        role: "ORGANIZATION_ADMIN"
      }
    });
  }

  console.log('Deployment seed completed successfully!');
}

main()
  .catch((e) => {
    console.error('Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
