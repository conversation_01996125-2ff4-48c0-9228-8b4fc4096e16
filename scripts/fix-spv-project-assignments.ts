#!/usr/bin/env tsx

/**
 * Migration script to fix existing project assignments
 * This script updates projects' spvId field based on existing ProjectAssignment records
 * 
 * Run with: npx tsx scripts/fix-spv-project-assignments.ts
 */

import { PrismaClient } from '@prisma/client';

const db = new PrismaClient();

async function main() {
  console.log('🔧 Starting SPV project assignment migration...');

  try {
    // Find all project assignments where the project's spvId doesn't match the SPV user's spvId
    const assignmentsToFix = await db.projectAssignment.findMany({
      where: {
        isActive: true,
      },
      include: {
        project: {
          select: {
            id: true,
            name: true,
            spvId: true,
          },
        },
        spvUser: {
          select: {
            id: true,
            spvId: true,
            spv: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });

    console.log(`📊 Found ${assignmentsToFix.length} project assignments to check`);

    const projectsToUpdate: Array<{ projectId: string; currentSpvId: string | null; newSpvId: string; projectName: string; spvName: string }> = [];

    for (const assignment of assignmentsToFix) {
      const project = assignment.project;
      const spvUser = assignment.spvUser;

      // Check if project's spvId doesn't match the SPV user's spvId
      if (project.spvId !== spvUser.spvId) {
        projectsToUpdate.push({
          projectId: project.id,
          currentSpvId: project.spvId,
          newSpvId: spvUser.spvId,
          projectName: project.name,
          spvName: spvUser.spv.name,
        });
      }
    }

    console.log(`🔄 Found ${projectsToUpdate.length} projects that need spvId updates`);

    if (projectsToUpdate.length === 0) {
      console.log('✅ All project assignments are already correctly linked!');
      return;
    }

    // Show what will be updated
    console.log('\n📋 Projects to update:');
    for (const update of projectsToUpdate) {
      console.log(`  • "${update.projectName}" -> SPV: "${update.spvName}"`);
      console.log(`    Current spvId: ${update.currentSpvId || 'null'} -> New spvId: ${update.newSpvId}`);
    }

    // Perform the updates in a transaction
    console.log('\n🚀 Updating projects...');
    
    const updateResults = await db.$transaction(async (tx) => {
      const results = [];
      
      for (const update of projectsToUpdate) {
        const result = await tx.project.update({
          where: { id: update.projectId },
          data: { spvId: update.newSpvId },
          select: {
            id: true,
            name: true,
            spvId: true,
          },
        });
        results.push(result);
      }
      
      return results;
    });

    console.log(`✅ Successfully updated ${updateResults.length} projects!`);

    // Verify the updates
    console.log('\n🔍 Verifying updates...');
    for (const result of updateResults) {
      const expectedUpdate = projectsToUpdate.find(u => u.projectId === result.id);
      if (result.spvId === expectedUpdate?.newSpvId) {
        console.log(`  ✅ "${result.name}" - spvId correctly set to ${result.spvId}`);
      } else {
        console.log(`  ❌ "${result.name}" - spvId mismatch! Expected: ${expectedUpdate?.newSpvId}, Got: ${result.spvId}`);
      }
    }

    console.log('\n🎉 Migration completed successfully!');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await db.$disconnect();
  }
}

// Run the migration
main()
  .catch((error) => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
