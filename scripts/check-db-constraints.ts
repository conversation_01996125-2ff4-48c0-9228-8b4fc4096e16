#!/usr/bin/env tsx

/**
 * Check database constraints and schema state
 */

import { PrismaClient } from '@prisma/client';

const db = new PrismaClient();

async function main() {
  console.log('🔍 Checking database constraints and schema...');

  try {
    // Check Transaction table constraints
    const constraints = await db.$queryRaw`
      SELECT 
        conname as constraint_name,
        contype as constraint_type,
        pg_get_constraintdef(oid) as constraint_definition
      FROM pg_constraint 
      WHERE conrelid = 'Transaction'::regclass
      ORDER BY conname;
    `;
    
    console.log('\n📋 Transaction table constraints:');
    (constraints as any[]).forEach((constraint: any) => {
      console.log(`  • ${constraint.constraint_name} (${constraint.constraint_type}): ${constraint.constraint_definition}`);
    });

    // Check if SPV-related tables exist
    const spvTables = await db.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND (table_name LIKE '%spv%' OR table_name LIKE '%project%')
      ORDER BY table_name;
    `;
    
    console.log('\n🎯 SPV and Project related tables:');
    (spvTables as any[]).forEach((table: any) => {
      console.log(`  ✅ ${table.table_name}`);
    });

    // Check if SPV User enum exists
    const spvUserRoleEnum = await db.$queryRaw`
      SELECT enumlabel 
      FROM pg_enum 
      WHERE enumtypid = (
        SELECT oid 
        FROM pg_type 
        WHERE typname = 'SPVUserRole'
      )
      ORDER BY enumlabel;
    `;
    
    if ((spvUserRoleEnum as any[]).length > 0) {
      console.log('\n👥 SPVUserRole enum values:');
      (spvUserRoleEnum as any[]).forEach((role: any) => {
        console.log(`  • ${role.enumlabel}`);
      });
    } else {
      console.log('\n❌ SPVUserRole enum not found');
    }

    // Check UserRole enum to see if SPV_USER is included
    const userRoleEnum = await db.$queryRaw`
      SELECT enumlabel 
      FROM pg_enum 
      WHERE enumtypid = (
        SELECT oid 
        FROM pg_type 
        WHERE typname = 'UserRole'
      )
      ORDER BY enumlabel;
    `;
    
    console.log('\n👤 UserRole enum values:');
    (userRoleEnum as any[]).forEach((role: any) => {
      console.log(`  • ${role.enumlabel}`);
    });

  } catch (error) {
    console.error('❌ Error checking database:', error);
    throw error;
  } finally {
    await db.$disconnect();
  }
}

// Run the check
main()
  .catch((error) => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
