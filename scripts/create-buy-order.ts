import { OrderType, OrderStatus } from '@prisma/client';

import { db as prisma } from '../src/lib/db';

async function main() {
  console.log('Creating sample buy order...');

  // Find a user with an organization
  const buyer = await prisma.user.findFirst({
    where: {
      NOT: {
        organizationId: null
      }
    },
    include: {
      organization: true
    }
  });

  if (!buyer || !buyer.organization) {
    console.error('No buyer user with an organization found.');
    return;
  }

  // Find a carbon credit from a different organization
  const carbonCredit = await prisma.carbonCredit.findFirst({
    where: {
      status: 'LISTED',
      NOT: {
        organizationId: buyer.organization.id
      }
    },
    include: {
      user: true,
      organization: true
    }
  });

  if (!carbonCredit) {
    console.error('No suitable carbon credit found for purchase.');
    return;
  }

  // Find the seller user
  const seller = await prisma.user.findFirst({
    where: {
      organizationId: carbonCredit.organizationId
    }
  });

  if (!seller) {
    console.error('No seller user found for the carbon credit.');
    return;
  }

  console.log(`Found buyer: ${buyer.email} (${buyer.organization.name})`);
  console.log(`Found carbon credit: ${carbonCredit.name} from ${carbonCredit.organization.name}`);
  console.log(`Found seller: ${seller.email}`);

  // Create a buy order
  const quantity = Math.min(100, carbonCredit.availableQuantity);
  const order = await prisma.order.create({
    data: {
      type: OrderType.BUY,
      quantity,
      price: carbonCredit.price,
      status: OrderStatus.PENDING,
      buyer: {
        connect: {
          id: buyer.id
        }
      },
      seller: {
        connect: {
          id: seller.id
        }
      },
      carbonCredit: {
        connect: {
          id: carbonCredit.id
        }
      }
    }
  });

  console.log(`Created buy order: ${order.id} for ${quantity} tons at ₹${order.price} per ton`);
  console.log('Buy order created successfully!');
}

main()
  .catch((e) => {
    console.error('Error creating buy order:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
