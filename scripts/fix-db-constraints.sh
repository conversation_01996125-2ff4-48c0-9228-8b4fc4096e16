#!/bin/bash

# This script fixes database constraint issues that might occur during migrations
# It directly connects to the database and fixes the constraints

# Color codes for terminal output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Check if we need to use sudo for Docker commands
if [ -z "$DOCKER" ]; then
  if sudo docker info &> /dev/null; then
    echo -e "${YELLOW}Docker requires sudo privileges. Using sudo for Docker commands.${NC}"
    DOCKER="sudo docker"
    DOCKER_COMPOSE="sudo docker compose"
  elif docker info &> /dev/null; then
    DOCKER="docker"
    DOCKER_COMPOSE="docker compose"
  else
    echo -e "${RED}Cannot access Docker daemon. Please check Docker installation or permissions.${NC}"
    echo -e "${YELLOW}Try running this script with sudo or add your user to the docker group:${NC}"
    echo -e "  sudo usermod -aG docker $USER && newgrp docker"
    exit 1
  fi
fi

echo -e "${BLUE}Fixing database constraints...${NC}"

# SQL script to fix the constraint issue
SQL_SCRIPT=$(cat <<EOF
-- Check and fix constraints
DO \$\$
BEGIN
    -- Check if Transaction_parentValuation_fkey already exists
    IF EXISTS (
        SELECT 1 FROM pg_constraint
        WHERE conname = 'Transaction_parentValuation_fkey'
    ) THEN
        -- If it exists, we don't need to do anything
        RAISE NOTICE 'Constraint Transaction_parentValuation_fkey already exists';
    ELSE
        -- If it doesn't exist, check if Transaction_assetValuationId_fkey exists
        IF EXISTS (
            SELECT 1 FROM pg_constraint
            WHERE conname = 'Transaction_assetValuationId_fkey'
        ) THEN
            -- Rename the constraint
            ALTER TABLE "Transaction" RENAME CONSTRAINT "Transaction_assetValuationId_fkey" TO "Transaction_parentValuation_fkey";
            RAISE NOTICE 'Renamed constraint Transaction_assetValuationId_fkey to Transaction_parentValuation_fkey';
        ELSE
            RAISE NOTICE 'Neither constraint exists, no action needed';
        END IF;
    END IF;

    -- Additional fix: Check if the Transaction table has the correct columns
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'Transaction' AND column_name = 'assetValuationId'
    ) THEN
        RAISE NOTICE 'Transaction table has assetValuationId column';

        -- Make sure the foreign key references are correct
        -- This is a more aggressive fix that might be needed in some cases
        BEGIN
            -- Drop the constraint if it exists (will be recreated properly)
            IF EXISTS (
                SELECT 1 FROM pg_constraint
                WHERE conname = 'Transaction_parentValuation_fkey'
            ) THEN
                ALTER TABLE "Transaction" DROP CONSTRAINT "Transaction_parentValuation_fkey";
                RAISE NOTICE 'Dropped constraint Transaction_parentValuation_fkey';
            END IF;

            -- Create the constraint with the correct name
            ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_parentValuation_fkey"
                FOREIGN KEY ("assetValuationId") REFERENCES "AssetValuation"(id);
            RAISE NOTICE 'Created constraint Transaction_parentValuation_fkey';
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Error fixing foreign key: %', SQLERRM;
        END;
    END IF;
END \$\$;
EOF
)

# Execute the SQL script in the database container
echo -e "${BLUE}Executing SQL fix...${NC}"
$DOCKER_COMPOSE exec -T db psql -U postgres -d carbon_exchange -c "$SQL_SCRIPT"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}Database constraints fixed successfully!${NC}"
else
    echo -e "${RED}Failed to fix database constraints.${NC}"
    exit 1
fi

echo -e "${BLUE}You can now try running the deployment script again.${NC}"
