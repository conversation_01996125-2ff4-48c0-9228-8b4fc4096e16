#!/usr/bin/env node

/**
 * This script helps set up an Alchemy Gas Manager Policy for the Carbon Exchange platform.
 * It provides instructions on how to create a policy in the Alchemy Dashboard.
 */

const chalk = require('chalk');
const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs');

// Load environment variables
dotenv.config({ path: path.resolve(process.cwd(), '.env') });

console.log(chalk.green('Carbon Exchange - Alchemy Gas Manager Setup Guide'));
console.log(chalk.yellow('================================================='));
console.log('');

// Check if Alchemy API key is set
const alchemyApiKey = process.env.ALCHEMY_API_KEY;
if (!alchemyApiKey) {
  console.log(chalk.red('Error: ALCHEMY_API_KEY is not set in your .env file.'));
  console.log('Please set your Alchemy API key before continuing.');
  process.exit(1);
}

// Check if Gas Manager Policy ID is already set
const gasManagerPolicyId = process.env.ALCHEMY_GAS_MANAGER_POLICY_ID;
if (gasManagerPolicyId) {
  console.log(chalk.yellow('Note: You already have ALCHEMY_GAS_MANAGER_POLICY_ID set in your .env file.'));
  console.log(`Current value: ${gasManagerPolicyId}`);
  console.log('If you want to create a new policy, continue with the steps below.');
  console.log('');
}

// Instructions
console.log(chalk.cyan('Follow these steps to set up your Gas Manager Policy:'));
console.log('');
console.log('1. Go to the Alchemy Dashboard: https://dashboard.alchemy.com/');
console.log('2. Select your app or create a new one');
console.log('3. Navigate to "Account Kit" > "Gas Manager"');
console.log('4. Click "Create Policy"');
console.log('5. Configure your policy with the following settings:');
console.log('   - Policy Name: Carbon Exchange Gas Policy');
console.log('   - Networks: Select the networks you want to support (e.g., Sepolia for testing)');
console.log('   - Gas Limit: Set appropriate limits (e.g., 5,000,000 for most operations)');
console.log('   - Rules:');
console.log('     - Add rules to limit gas usage by user, contract, or function');
console.log('     - Consider adding a whitelist of contract addresses for your carbon credit contracts');
console.log('6. Click "Create Policy"');
console.log('7. Copy the Policy ID');
console.log('');

console.log(chalk.cyan('After creating your policy:'));
console.log('');
console.log('1. Add the Policy ID to your .env file:');
console.log('   ALCHEMY_GAS_MANAGER_POLICY_ID=your-policy-id');
console.log('');
console.log('2. If deploying to production, also add it to your .env.production file');
console.log('');
console.log('3. Update your Docker environment variables if using Docker');
console.log('');

console.log(chalk.yellow('Important Notes:'));
console.log('- Gas Manager policies have usage limits based on your Alchemy plan');
console.log('- Monitor your usage in the Alchemy Dashboard to avoid unexpected charges');
console.log('- Consider implementing fallback mechanisms for when gas sponsorship limits are reached');
console.log('');

console.log(chalk.green('For more information, visit:'));
console.log('https://docs.alchemy.com/reference/gas-manager-overview');
console.log('');

// Check if we should update the .env file
if (!gasManagerPolicyId) {
  console.log(chalk.cyan('Would you like to add a placeholder for ALCHEMY_GAS_MANAGER_POLICY_ID to your .env file? (y/n)'));
  process.stdin.once('data', (data) => {
    const input = data.toString().trim().toLowerCase();
    if (input === 'y' || input === 'yes') {
      try {
        const envPath = path.resolve(process.cwd(), '.env');
        let envContent = fs.readFileSync(envPath, 'utf8');
        
        if (envContent.includes('ALCHEMY_GAS_MANAGER_POLICY_ID=')) {
          console.log(chalk.yellow('ALCHEMY_GAS_MANAGER_POLICY_ID already exists in .env file.'));
        } else {
          // Find the blockchain section and add the variable
          if (envContent.includes('# Blockchain')) {
            envContent = envContent.replace(
              '# Blockchain',
              '# Blockchain\nALCHEMY_GAS_MANAGER_POLICY_ID=your-gas-manager-policy-id'
            );
          } else {
            // If no blockchain section, add at the end
            envContent += '\n# Alchemy Gas Manager\nALCHEMY_GAS_MANAGER_POLICY_ID=your-gas-manager-policy-id\n';
          }
          
          fs.writeFileSync(envPath, envContent);
          console.log(chalk.green('Added ALCHEMY_GAS_MANAGER_POLICY_ID to .env file.'));
        }
      } catch (error) {
        console.log(chalk.red(`Error updating .env file: ${error.message}`));
      }
    }
    process.exit(0);
  });
} else {
  process.exit(0);
}
