#!/bin/bash

# Script to fix tenant isolation imports
# Usage: ./scripts/fix-tenant-isolation.sh

echo "Fixing tenant isolation imports..."

# Get all files that still import from multi-tenant
files=$(grep -r "@/lib/multi-tenant" --include="*.ts" --include="*.tsx" src | cut -d: -f1 | sort | uniq)

# Process each file
for file in $files; do
  echo "Processing $file"
  
  # Create a temporary file
  temp_file=$(mktemp)
  
  # Read the file line by line
  while IFS= read -r line; do
    # Check if the line contains an import from multi-tenant
    if [[ $line == *"@/lib/multi-tenant"* ]]; then
      # Skip this line (we'll add the correct import later)
      continue
    else
      # Keep all other lines
      echo "$line" >> "$temp_file"
    fi
  done < "$file"
  
  # Check if there's already an import from tenant-isolation
  if grep -q "@/lib/tenant-isolation" "$temp_file"; then
    # Find the line with the tenant-isolation import
    tenant_line=$(grep "@/lib/tenant-isolation" "$temp_file")
    
    # Extract the imported symbols
    symbols=$(echo "$tenant_line" | sed -E 's/import \{ (.*) \} from.*/\1/')
    
    # Get the multi-tenant import line from the original file
    multi_tenant_line=$(grep "@/lib/multi-tenant" "$file")
    
    # Extract the imported symbols from multi-tenant
    multi_tenant_symbols=$(echo "$multi_tenant_line" | sed -E 's/import \{ (.*) \} from.*/\1/')
    
    # Create a new import line with all symbols
    new_import="import { $symbols, $multi_tenant_symbols } from \"@/lib/tenant-isolation\";"
    
    # Replace the tenant-isolation import with the new import
    sed -i '' "s|$tenant_line|$new_import|" "$temp_file"
  else
    # Get the multi-tenant import line from the original file
    multi_tenant_line=$(grep "@/lib/multi-tenant" "$file")
    
    # Create a new import line that uses tenant-isolation instead
    new_import=$(echo "$multi_tenant_line" | sed 's/@\/lib\/multi-tenant/@\/lib\/tenant-isolation/')
    
    # Add the new import after the last import
    last_import_line=$(grep -n "import " "$temp_file" | tail -1 | cut -d: -f1)
    sed -i '' "${last_import_line}a\\
${new_import}" "$temp_file"
  fi
  
  # Replace the original file with the temporary file
  mv "$temp_file" "$file"
done

# Update dynamic imports in rbac files
rbac_files=$(grep -r "await import('@/lib/multi-tenant')" --include="*.ts" --include="*.tsx" src | cut -d: -f1 | sort | uniq)
for file in $rbac_files; do
  echo "Updating dynamic import in $file"
  sed -i '' "s/await import('@\/lib\/multi-tenant')/await import('@\/lib\/tenant-isolation')/g" "$file"
done

echo "Tenant isolation imports fixed successfully!"
