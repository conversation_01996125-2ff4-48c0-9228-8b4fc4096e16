#!/usr/bin/env python3

import os
import re
import sys

def replace_page_header_usage(file_path):
    """Replace PageHeaderWithBreadcrumb usage with simple header"""
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Pattern to match PageHeaderWithBreadcrumb usage
    pattern = r'<PageHeaderWithBreadcrumb\s+title=\{([^}]+)\}\s+description=\{([^}]+)\}[^>]*\/>'
    
    def replacement(match):
        title = match.group(1)
        description = match.group(2)
        return f'''<div>
            <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
            <p className="text-muted-foreground">{description}</p>
          </div>'''
    
    # Replace single-line usage
    content = re.sub(pattern, replacement, content)
    
    # Pattern for multi-line usage
    multiline_pattern = r'<PageHeaderWithBreadcrumb\s+title=\{([^}]+)\}\s+description=\{([^}]+)\}[^>]*>[^<]*</PageHeaderWithBreadcrumb>'
    content = re.sub(multiline_pattern, replacement, content, flags=re.DOTALL)
    
    # More complex pattern for multi-line with breadcrumb items
    complex_pattern = r'<PageHeaderWithBreadcrumb\s+title=\{([^}]+)\}\s+description=\{([^}]+)\}\s+breadcrumbItems=\{[^}]+\}[^>]*\/>'
    content = re.sub(complex_pattern, replacement, content)
    
    # Pattern for string literals
    string_pattern = r'<PageHeaderWithBreadcrumb\s+title="([^"]+)"\s+description="([^"]+)"[^>]*\/>'
    
    def string_replacement(match):
        title = match.group(1)
        description = match.group(2)
        return f'''<div>
            <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
            <p className="text-muted-foreground">{description}</p>
          </div>'''
    
    content = re.sub(string_pattern, string_replacement, content)
    
    with open(file_path, 'w') as f:
        f.write(content)
    
    return True

def main():
    # List of files to process
    files_to_process = [
        "src/app/dashboard/settings/page.tsx",
        "src/app/dashboard/transactions/page.tsx", 
        "src/app/dashboard/wallet/page.tsx",
        "src/app/compliance/reports/page.tsx",
        "src/app/compliance/kyc/page.tsx",
        "src/app/compliance/audit-trail/page.tsx",
        "src/app/admin/dashboard/page.tsx",
        "src/app/admin/users/page.tsx",
        "src/app/admin/organizations/page.tsx",
        "src/app/admin/settings/page.tsx",
        "src/app/admin/projects/page.tsx",
        "src/app/admin/verification/page.tsx"
    ]
    
    for file_path in files_to_process:
        if os.path.exists(file_path):
            print(f"Processing {file_path}...")
            try:
                replace_page_header_usage(file_path)
                print(f"✓ Processed {file_path}")
            except Exception as e:
                print(f"✗ Error processing {file_path}: {e}")
        else:
            print(f"✗ File not found: {file_path}")

if __name__ == "__main__":
    main()
