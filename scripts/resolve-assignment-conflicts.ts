#!/usr/bin/env tsx

/**
 * <PERSON>ript to resolve project assignment conflicts
 * This script removes assignments where the SPV user's SPV doesn't match the project's spvId
 * 
 * Run with: npx tsx scripts/resolve-assignment-conflicts.ts
 */

import { PrismaClient } from '@prisma/client';

const db = new PrismaClient();

async function main() {
  console.log('🔧 Resolving project assignment conflicts...');

  try {
    // Find all project assignments where the SPV user's spvId doesn't match the project's spvId
    const conflictingAssignments = await db.projectAssignment.findMany({
      where: {
        isActive: true,
      },
      include: {
        project: {
          select: {
            id: true,
            name: true,
            spvId: true,
          },
        },
        spvUser: {
          include: {
            spv: {
              select: {
                id: true,
                name: true,
              },
            },
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
      },
    });

    console.log(`📊 Found ${conflictingAssignments.length} total assignments to check`);

    // Filter for actual conflicts
    const assignmentsToRemove = conflictingAssignments.filter(assignment => {
      const projectSpvId = assignment.project.spvId;
      const userSpvId = assignment.spvUser.spvId;
      
      // If project has no spvId, it's not a conflict (yet)
      if (!projectSpvId) {
        return false;
      }
      
      // If the SPV IDs don't match, it's a conflict
      return projectSpvId !== userSpvId;
    });

    console.log(`⚠️  Found ${assignmentsToRemove.length} conflicting assignments to remove`);

    if (assignmentsToRemove.length === 0) {
      console.log('✅ No conflicts found! All assignments are correctly aligned.');
      return;
    }

    // Show what will be removed
    console.log('\n📋 Conflicting assignments to remove:');
    const projectGroups = new Map<string, typeof assignmentsToRemove>();
    
    for (const assignment of assignmentsToRemove) {
      const projectId = assignment.project.id;
      if (!projectGroups.has(projectId)) {
        projectGroups.set(projectId, []);
      }
      projectGroups.get(projectId)!.push(assignment);
    }

    for (const [projectId, assignments] of projectGroups.entries()) {
      const project = assignments[0].project;
      console.log(`\n  📋 Project: "${project.name}"`);
      console.log(`     Project spvId: ${project.spvId}`);
      console.log(`     Removing ${assignments.length} conflicting assignments:`);
      
      for (const assignment of assignments) {
        console.log(`       • User: ${assignment.spvUser.user.name} (${assignment.spvUser.user.email})`);
        console.log(`         SPV: ${assignment.spvUser.spv.name} (${assignment.spvUser.spvId})`);
        console.log(`         Assignment ID: ${assignment.id}`);
      }
    }

    // Ask for confirmation (in a real scenario, you might want to add a --confirm flag)
    console.log(`\n⚠️  This will remove ${assignmentsToRemove.length} conflicting assignments.`);
    console.log('   Users will lose access to projects that don\'t belong to their SPV.');
    
    // Perform the cleanup in a transaction
    console.log('\n🚀 Removing conflicting assignments...');
    
    const removedAssignments = await db.$transaction(async (tx) => {
      const results = [];
      
      for (const assignment of assignmentsToRemove) {
        const result = await tx.projectAssignment.update({
          where: { id: assignment.id },
          data: { isActive: false },
          select: {
            id: true,
            project: {
              select: {
                name: true,
              },
            },
            spvUser: {
              select: {
                user: {
                  select: {
                    name: true,
                  },
                },
                spv: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
        });
        results.push(result);
      }
      
      return results;
    });

    console.log(`✅ Successfully deactivated ${removedAssignments.length} conflicting assignments!`);

    // Verify the cleanup
    console.log('\n🔍 Verifying cleanup...');
    for (const result of removedAssignments) {
      console.log(`  ✅ Removed: ${result.spvUser.user.name} from "${result.project.name}" (SPV: ${result.spvUser.spv.name})`);
    }

    // Final verification - check for remaining conflicts
    const remainingConflicts = await db.projectAssignment.findMany({
      where: {
        isActive: true,
        project: {
          spvId: {
            not: null,
          },
        },
      },
      include: {
        project: {
          select: {
            spvId: true,
          },
        },
        spvUser: {
          select: {
            spvId: true,
          },
        },
      },
    });

    const stillConflicted = remainingConflicts.filter(assignment => 
      assignment.project.spvId !== assignment.spvUser.spvId
    );

    if (stillConflicted.length === 0) {
      console.log('\n🎉 All conflicts resolved! Project assignments are now consistent.');
    } else {
      console.log(`\n⚠️  Warning: ${stillConflicted.length} conflicts still remain. Manual review may be needed.`);
    }

  } catch (error) {
    console.error('❌ Conflict resolution failed:', error);
    throw error;
  } finally {
    await db.$disconnect();
  }
}

// Run the conflict resolution
main()
  .catch((error) => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
