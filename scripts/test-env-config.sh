#!/bin/bash

# Test script to verify environment configuration is working correctly
# This script tests that database credentials are properly read from .env

set -e

echo "🧪 Testing Environment Configuration..."

# Function to load environment variables from .env file
load_env_vars() {
    local env_file=".env"
    
    if [ ! -f "$env_file" ]; then
        echo "❌ .env file not found!"
        exit 1
    fi
    
    # Source the .env file
    set -a  # automatically export all variables
    source "$env_file"
    set +a  # stop automatically exporting
    
    # Validate and set defaults for required database variables
    validate_and_set_defaults
}

# Function to validate and set defaults for database variables
validate_and_set_defaults() {
    # Set defaults if variables are not set or empty
    DATABASE_HOST=${DATABASE_HOST:-localhost}
    DATABASE_PORT=${DATABASE_PORT:-5433}
    DATABASE_USER=${DATABASE_USER:-postgres}
    DATABASE_PASSWORD=${DATABASE_PASSWORD:-root}
    DATABASE_NAME=${DATABASE_NAME:-carbon_exchange}
    
    # Construct DATABASE_URL if not provided or if it doesn't match current settings
    local expected_url="postgresql://${DATABASE_USER}:${DATABASE_PASSWORD}@${DATABASE_HOST}:${DATABASE_PORT}/${DATABASE_NAME}"
    if [ -z "$DATABASE_URL" ] || [ "$DATABASE_URL" != "$expected_url" ]; then
        DATABASE_URL="$expected_url"
        echo "🔧 Constructed DATABASE_URL: $DATABASE_URL"
    fi
    
    # Export variables for use in this script and child processes
    export DATABASE_HOST DATABASE_PORT DATABASE_USER DATABASE_PASSWORD DATABASE_NAME DATABASE_URL
}

# Load environment variables
load_env_vars

echo ""
echo "📋 Environment Variables Loaded:"
echo "   DATABASE_HOST: $DATABASE_HOST"
echo "   DATABASE_PORT: $DATABASE_PORT"
echo "   DATABASE_USER: $DATABASE_USER"
echo "   DATABASE_PASSWORD: $DATABASE_PASSWORD"
echo "   DATABASE_NAME: $DATABASE_NAME"
echo "   DATABASE_URL: $DATABASE_URL"
echo ""

# Test that the DATABASE_URL is properly constructed
expected_url="postgresql://${DATABASE_USER}:${DATABASE_PASSWORD}@${DATABASE_HOST}:${DATABASE_PORT}/${DATABASE_NAME}"
if [ "$DATABASE_URL" = "$expected_url" ]; then
    echo "✅ DATABASE_URL is correctly constructed"
else
    echo "❌ DATABASE_URL mismatch!"
    echo "   Expected: $expected_url"
    echo "   Actual:   $DATABASE_URL"
    exit 1
fi

# Test that all required variables are set
required_vars=("DATABASE_HOST" "DATABASE_PORT" "DATABASE_USER" "DATABASE_PASSWORD" "DATABASE_NAME" "DATABASE_URL")
missing_vars=()

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -eq 0 ]; then
    echo "✅ All required database environment variables are set"
else
    echo "❌ Missing required environment variables: ${missing_vars[*]}"
    exit 1
fi

echo ""
echo "🎉 Environment configuration test passed!"
echo "   The scripts will use these database credentials"
echo "   Both production and development scripts are ready to use"
