#!/usr/bin/env node

/**
 * This script sets up the database for development and build processes.
 * It starts the Docker container for PostgreSQL and runs Prisma migrations.
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  red: '\x1b[31m',
};

// Log with color
function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

// Execute a command and return its output
function exec(command, options = {}) {
  try {
    return execSync(command, {
      stdio: options.silent ? 'pipe' : 'inherit',
      ...options,
    });
  } catch (error) {
    if (options.ignoreError) {
      return null;
    }
    throw error;
  }
}

// Check if Docker is running
function checkDocker() {
  try {
    exec('docker info', { silent: true });
    return true;
  } catch (error) {
    return false;
  }
}

// Check if the database container is running
function isDbContainerRunning() {
  try {
    const output = exec('docker ps --filter "name=carbon-exchange-db" --format "{{.Names}}"', {
      silent: true,
      encoding: 'utf-8',
    });
    return output && output.toString().trim() === 'carbon-exchange-db';
  } catch (error) {
    return false;
  }
}

// Start the database container
function startDbContainer() {
  log('Starting PostgreSQL container...', colors.blue);

  // Check if the container exists but is stopped
  const containerExists = exec(
    'docker ps -a --filter "name=carbon-exchange-db" --format "{{.Names}}"',
    { silent: true, encoding: 'utf-8' }
  ).toString().trim() === 'carbon-exchange-db';

  if (containerExists) {
    log('Container exists, starting it...', colors.yellow);
    exec('docker start carbon-exchange-db');
  } else {
    log('Creating and starting container...', colors.yellow);
    exec('docker-compose up -d db');

    // Rename the container for easier reference
    exec('docker rename carbon-exchange_db_1 carbon-exchange-db', { ignoreError: true });
  }

  log('Waiting for PostgreSQL to be ready...', colors.blue);
  // Wait for PostgreSQL to be ready
  let retries = 30;
  while (retries > 0) {
    try {
      exec(
        'docker exec carbon-exchange-db pg_isready -U postgres',
        { silent: true }
      );
      log('PostgreSQL is ready!', colors.green);
      break;
    } catch (error) {
      process.stdout.write('.');
      retries--;
      if (retries === 0) {
        log('\nFailed to connect to PostgreSQL after multiple attempts', colors.red);
        process.exit(1);
      }
      // Wait 1 second before trying again
      exec('sleep 1', { silent: true });
    }
  }
  process.stdout.write('\n');
}

// Run Prisma migrations
function runPrismaMigrations() {
  log('Running Prisma migrations...', colors.blue);
  exec('pnpm exec prisma migrate deploy');
  log('Migrations completed successfully!', colors.green);
}

// Generate Prisma client
function generatePrismaClient() {
  log('Generating Prisma client...', colors.blue);
  exec('pnpm exec prisma generate');
  log('Prisma client generated successfully!', colors.green);
}

// Check if we're in a build environment (CI/CD)
function isBuildEnvironment() {
  return process.env.CI === 'true' || process.env.NEXT_PHASE === 'phase-production-build';
}

// Main function
async function main() {
  log('Setting up database for development and build...', colors.blue);

  // Check if Docker is running
  if (!checkDocker()) {
    log('Docker is not running.', colors.yellow);

    if (isBuildEnvironment()) {
      log('Running in build environment, generating Prisma client only...', colors.blue);
      generatePrismaClient();
      log('Prisma client generated successfully!', colors.green);
      return;
    } else {
      log('For local development, please start Docker and try again.', colors.red);
      process.exit(1);
    }
  }

  // Start the database container if it's not running
  if (!isDbContainerRunning()) {
    startDbContainer();
  } else {
    log('PostgreSQL container is already running', colors.green);
  }

  // Run Prisma migrations
  runPrismaMigrations();

  // Generate Prisma client
  generatePrismaClient();

  log('Database setup completed successfully!', colors.green);
}

// Run the main function
main().catch((error) => {
  log(`Error: ${error.message}`, colors.red);
  process.exit(1);
});
