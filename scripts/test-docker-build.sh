#!/bin/bash

# Test Docker Build Script
# This script tests the Docker build process locally

set -e  # Exit on any error

echo "🐳 Testing Docker Build Process..."
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "Dockerfile" ]; then
    print_error "Dockerfile not found. Please run this script from the project root."
    exit 1
fi

# Clean up any existing build
print_status "Cleaning up previous Docker builds..."
docker system prune -f > /dev/null 2>&1 || true

# Build the Docker image
print_status "Building Docker image..."
echo "This may take several minutes..."

BUILD_START=$(date +%s)

if docker build -t carbonx-test . --no-cache; then
    BUILD_END=$(date +%s)
    BUILD_TIME=$((BUILD_END - BUILD_START))
    print_success "Docker build completed successfully in ${BUILD_TIME} seconds!"
else
    print_error "Docker build failed. Check the error messages above."
    exit 1
fi

# Test the built image
print_status "Testing the built image..."

# Check if the image was created
if docker images | grep -q "carbonx-test"; then
    print_success "Docker image 'carbonx-test' created successfully"
else
    print_error "Docker image was not created"
    exit 1
fi

# Get image size
IMAGE_SIZE=$(docker images carbonx-test --format "table {{.Size}}" | tail -n 1)
print_status "Image size: $IMAGE_SIZE"

# Test running the container (quick test)
print_status "Testing container startup..."

CONTAINER_ID=$(docker run -d -p 3001:3000 carbonx-test)

if [ $? -eq 0 ]; then
    print_success "Container started successfully with ID: $CONTAINER_ID"
    
    # Wait a moment for the container to start
    sleep 5
    
    # Check if container is still running
    if docker ps | grep -q "$CONTAINER_ID"; then
        print_success "Container is running successfully"
        
        # Test if the application responds
        print_status "Testing application response..."
        if curl -f http://localhost:3001 > /dev/null 2>&1; then
            print_success "Application is responding on port 3001"
        else
            print_warning "Application may not be fully ready yet (this is normal)"
        fi
    else
        print_error "Container stopped unexpectedly"
        docker logs "$CONTAINER_ID"
    fi
    
    # Clean up test container
    print_status "Cleaning up test container..."
    docker stop "$CONTAINER_ID" > /dev/null 2>&1
    docker rm "$CONTAINER_ID" > /dev/null 2>&1
    
else
    print_error "Failed to start container"
    exit 1
fi

# Show final results
echo ""
echo "=================================="
print_success "🎉 Docker build test completed successfully!"
echo ""
print_status "Image details:"
docker images carbonx-test --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
echo ""
print_status "To run the container:"
echo "  docker run -p 3000:3000 carbonx-test"
echo ""
print_status "To push to registry (after tagging):"
echo "  docker tag carbonx-test your-registry/carbonx:latest"
echo "  docker push your-registry/carbonx:latest"
echo ""

# Optional: Clean up test image
read -p "Remove test image? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    docker rmi carbonx-test
    print_success "Test image removed"
fi

print_success "Docker build test completed! 🐳"
