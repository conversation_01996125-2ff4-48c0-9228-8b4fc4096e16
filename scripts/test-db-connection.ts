#!/usr/bin/env tsx

/**
 * Test script to check database connection and basic functionality
 */

import { PrismaClient } from '@prisma/client';

const db = new PrismaClient();

async function main() {
  console.log('🔍 Testing database connection...');

  try {
    // Test basic connection
    await db.$connect();
    console.log('✅ Database connection successful');

    // Test if we can query the database
    const result = await db.$queryRaw`SELECT 1 as test`;
    console.log('✅ Database query successful:', result);

    // Check if tables exist
    const tables = await db.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name;
    `;
    console.log(`📊 Found ${(tables as any[]).length} tables in database:`);
    (tables as any[]).forEach((table: any) => {
      console.log(`  • ${table.table_name}`);
    });

    // Check if SPV-related tables exist
    const spvTables = (tables as any[]).filter((table: any) => 
      table.table_name.includes('spv') || 
      table.table_name.includes('project')
    );
    
    if (spvTables.length > 0) {
      console.log('\n🎯 SPV-related tables found:');
      spvTables.forEach((table: any) => {
        console.log(`  ✅ ${table.table_name}`);
      });
    } else {
      console.log('\n❌ No SPV-related tables found - database needs migration');
    }

  } catch (error) {
    console.error('❌ Database connection failed:', error);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Suggestions:');
      console.log('  1. Check if PostgreSQL is running on localhost:5433');
      console.log('  2. Start the database with: docker compose up db -d');
      console.log('  3. Or check if the database service is running');
    }
    
    throw error;
  } finally {
    await db.$disconnect();
  }
}

// Run the test
main()
  .catch((error) => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
