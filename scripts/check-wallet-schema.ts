import { db as prisma } from '../src/lib/db';

async function main() {
  try {
    // Get the Prisma schema for the Wallet model
    const walletSchema = await prisma.$queryRaw`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'Wallet'
    `;

    console.log('Wallet schema:');
    console.table(walletSchema);

    // Try to create a test wallet
    const testWallet = await prisma.wallet.create({
      data: {
        address: `0x${Math.random().toString(16).substring(2, 42)}`,
        network: 'ethereum',
        chainId: 1,
        isTestnet: true,
        isSmartWallet: false,
        balance: 0,
        organization: {
          connect: {
            id: 'cm9mx0oy0000cldev5h2h8huj'
          }
        }
      }
    });

    console.log('Test wallet created successfully:', testWallet);
  } catch (error) {
    console.error('Error:', error);
  }
}

main()
  .catch((e) => {
    console.error('Error checking wallet schema:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
