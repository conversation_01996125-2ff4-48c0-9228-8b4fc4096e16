#!/bin/bash

# Colors for console output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Determine the command to use for pnpm
if command -v pnpm &> /dev/null; then
    PNPM_CMD="pnpm"
else
    PNPM_CMD="npx pnpm"
fi

# Determine the command to use for docker-compose
if command -v docker-compose &> /dev/null; then
    DOCKER_COMPOSE="docker-compose"
elif command -v docker &> /dev/null && docker compose version &> /dev/null; then
    DOCKER_COMPOSE="docker compose"
else
    echo -e "${RED}Neither docker-compose nor docker compose is available. Please install Docker and Docker Compose.${NC}"
    exit 1
fi

# Create a log directory if it doesn't exist
mkdir -p logs

# Create a timestamp for the log file
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="logs/migration_fix_${TIMESTAMP}.log"

# Function to log messages
log() {
    local message="$1"
    local color="$2"
    echo -e "${color}${message}${NC}"
    echo "[$(date +"%Y-%m-%d %H:%M:%S")] $message" >> "$LOG_FILE"
}

# Function to execute a command and log the result
execute_command() {
    local command="$1"
    local description="$2"
    local allow_failure="${3:-false}"
    
    log "EXECUTING: $command" "$BLUE"
    echo "$ $command" >> "$LOG_FILE"
    
    if eval "$command" >> "$LOG_FILE" 2>&1; then
        log "SUCCESS: $description completed successfully" "$GREEN"
        return 0
    else
        local exit_code=$?
        if [ "$allow_failure" = "true" ]; then
            log "WARNING: $description failed (exit code: $exit_code)" "$YELLOW"
            log "Check the log file for details: $LOG_FILE" "$BLUE"
            return $exit_code
        else
            log "ERROR: $description failed (exit code: $exit_code)" "$RED"
            log "Check the log file for details: $LOG_FILE" "$BLUE"
            return $exit_code
        fi
    fi
}

# Main script starts here
log "=== Prisma Migration Fix Script ===" "$BLUE"
log "This script will fix migration issues by resetting the database and creating a clean migration." "$YELLOW"

# Step 1: Make sure the database is running
log "Checking if the database is running..." "$BLUE"
if ! $DOCKER_COMPOSE ps | grep -q "db.*running"; then
    log "PostgreSQL container is not running. Starting it..." "$YELLOW"
    execute_command "$DOCKER_COMPOSE up -d db" "Starting database container"
    
    # Wait for PostgreSQL to be ready
    log "Waiting for PostgreSQL to be ready..." "$BLUE"
    until $DOCKER_COMPOSE exec db pg_isready -U postgres; do
        echo -e "${YELLOW}PostgreSQL is not ready yet... waiting${NC}"
        sleep 2
    done
else
    log "PostgreSQL container is already running." "$GREEN"
fi

# Step 2: Reset the database (drop all tables)
log "Resetting the database..." "$BLUE"
log "This will drop all tables in the database." "$YELLOW"

SQL_SCRIPT=$(cat <<EOF
-- Drop all tables in the public schema
DO \$\$
DECLARE
    r RECORD;
BEGIN
    -- Disable triggers temporarily
    EXECUTE 'SET session_replication_role = replica';
    
    -- Drop all tables in the public schema
    FOR r IN (SELECT tablename FROM pg_tables WHERE schemaname = 'public') LOOP
        EXECUTE 'DROP TABLE IF EXISTS public.' || quote_ident(r.tablename) || ' CASCADE';
    END LOOP;
    
    -- Re-enable triggers
    EXECUTE 'SET session_replication_role = DEFAULT';
    
    -- Drop the _prisma_migrations table if it exists
    DROP TABLE IF EXISTS _prisma_migrations;
END \$\$;
EOF
)

execute_command "$DOCKER_COMPOSE exec -T db psql -U postgres -d carbon_exchange -c \"$SQL_SCRIPT\"" "Resetting database"

# Step 3: Generate a clean migration
log "Generating a clean migration..." "$BLUE"
execute_command "$PNPM_CMD exec prisma migrate dev --name initial_migration --create-only" "Creating migration"

# Step 4: Apply the migration
log "Applying the migration..." "$BLUE"
execute_command "$PNPM_CMD exec prisma migrate deploy" "Applying migration"

# Step 5: Generate Prisma client
log "Generating Prisma client..." "$BLUE"
execute_command "$PNPM_CMD exec prisma generate" "Generating Prisma client"

# Step 6: Run the deployment seed
log "Running deployment seed..." "$BLUE"
execute_command "node scripts/deployment-seed.js" "Running deployment seed"

# Final message
log "Migration fix completed successfully!" "$GREEN"
log "The database has been reset and a clean migration has been applied." "$GREEN"
log "You can now continue with your development or deployment." "$GREEN"

exit 0
