import { UserRole } from '@prisma/client';
import bcrypt from 'bcryptjs';

import { db as prisma } from '../src/lib/db';

async function main() {
  const organizationId = 'cm9mx0oy0000cldev5h2h8huj'; // Replace with your organization ID

  console.log(`Creating test user for organization: ${organizationId}`);

  // Check if organization exists
  const organization = await prisma.organization.findUnique({
    where: {
      id: organizationId
    }
  });

  if (!organization) {
    console.error(`Organization with ID ${organizationId} not found.`);
    return;
  }

  console.log(`Found organization: ${organization.name}`);

  // Create test user
  const hashedPassword = await bcrypt.hash('Password123!', 10);

  // Check if test user already exists
  const existingUser = await prisma.user.findUnique({
    where: {
      email: '<EMAIL>'
    }
  });

  if (existingUser) {
    console.log(`Test user already exists: ${existingUser.email}`);

    // Update user to associate with the organization
    const updatedUser = await prisma.user.update({
      where: {
        id: existingUser.id
      },
      data: {
        organizationId
      }
    });

    console.log(`Updated test user: ${updatedUser.email} (${updatedUser.id}) with organization ID: ${updatedUser.organizationId}`);
  } else {
    // Create new test user
    const newUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Test User',
        password: hashedPassword,
        role: UserRole.ORGANIZATION_ADMIN,
        emailVerified: new Date(),
        organization: {
          connect: {
            id: organizationId
          }
        }
      }
    });

    console.log(`Created test user: ${newUser.email} (${newUser.id}) with organization ID: ${newUser.organizationId}`);
  }

  console.log('Test user creation complete!');
}

main()
  .catch((e) => {
    console.error('Error creating test user:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
