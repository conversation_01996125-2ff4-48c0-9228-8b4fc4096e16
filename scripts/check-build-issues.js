#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 Checking for potential build issues...\n');

// Common problematic imports that might cause build failures
const problematicImports = [
  'FilePdf', // Should be File
  'FileImage', // Should be Image
  'FileVideo', // Should be Video
  'FileAudio', // Should be Music
];

// Function to recursively find all TypeScript/React files
function findTsxFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
      findTsxFiles(filePath, fileList);
    } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// Check for problematic imports
function checkProblematicImports() {
  console.log('📦 Checking for problematic lucide-react imports...');

  const srcDir = path.join(process.cwd(), 'src');
  const files = findTsxFiles(srcDir);
  let issuesFound = false;

  files.forEach(file => {
    try {
      const content = fs.readFileSync(file, 'utf8');

      problematicImports.forEach(importName => {
        if (content.includes(importName)) {
          console.log(`❌ Found problematic import "${importName}" in: ${file}`);
          issuesFound = true;
        }
      });
    } catch (error) {
      console.log(`⚠️  Could not read file: ${file}`);
    }
  });

  if (!issuesFound) {
    console.log('✅ No problematic lucide-react imports found');
  }

  return !issuesFound;
}

// Check for syntax errors (missing closing braces)
function checkSyntaxErrors() {
  console.log('\n🔍 Checking for syntax errors...');

  const srcDir = path.join(process.cwd(), 'src');
  const files = findTsxFiles(srcDir);
  let issuesFound = false;

  files.forEach(file => {
    try {
      const content = fs.readFileSync(file, 'utf8');

      // Check for export function without closing brace
      const exportFunctionRegex = /export\s+function\s+\w+/g;
      const exportMatches = content.match(exportFunctionRegex);

      if (exportMatches && exportMatches.length > 0) {
        // Count opening and closing braces
        const openBraces = (content.match(/\{/g) || []).length;
        const closeBraces = (content.match(/\}/g) || []).length;

        if (openBraces !== closeBraces) {
          console.log(`❌ Brace mismatch in: ${file} (${openBraces} open, ${closeBraces} close)`);
          issuesFound = true;
        }

        // Check if file ends with ); instead of ); }
        if (content.trim().endsWith(');') && !content.trim().endsWith('); }')) {
          const lines = content.split('\n');
          const lastNonEmptyLine = lines.filter(line => line.trim()).pop();
          if (lastNonEmptyLine && lastNonEmptyLine.trim() === ');') {
            console.log(`❌ Missing closing brace in: ${file} (ends with ); instead of ); })`);
            issuesFound = true;
          }
        }
      }
    } catch (error) {
      console.log(`⚠️  Could not read file: ${file}`);
    }
  });

  if (!issuesFound) {
    console.log('✅ No syntax errors found');
  }

  return !issuesFound;
}

// Check for missing dependencies
function checkDependencies() {
  console.log('\n📋 Checking package.json dependencies...');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const requiredDeps = [
      'lucide-react',
      'next',
      'react',
      'react-dom',
      '@prisma/client',
      'prisma'
    ];
    
    let missingDeps = [];
    
    requiredDeps.forEach(dep => {
      if (!packageJson.dependencies[dep] && !packageJson.devDependencies[dep]) {
        missingDeps.push(dep);
      }
    });
    
    if (missingDeps.length > 0) {
      console.log(`❌ Missing dependencies: ${missingDeps.join(', ')}`);
      return false;
    } else {
      console.log('✅ All required dependencies found');
      return true;
    }
  } catch (error) {
    console.log('❌ Could not read package.json');
    return false;
  }
}

// Check TypeScript configuration
function checkTypeScriptConfig() {
  console.log('\n🔧 Checking TypeScript configuration...');
  
  try {
    if (fs.existsSync('tsconfig.json')) {
      const tsConfig = JSON.parse(fs.readFileSync('tsconfig.json', 'utf8'));
      console.log('✅ tsconfig.json found');
      
      // Check for common issues
      if (tsConfig.compilerOptions && tsConfig.compilerOptions.strict === false) {
        console.log('⚠️  TypeScript strict mode is disabled - this might cause issues');
      }
      
      return true;
    } else {
      console.log('❌ tsconfig.json not found');
      return false;
    }
  } catch (error) {
    console.log('❌ Could not parse tsconfig.json');
    return false;
  }
}

// Check Next.js configuration
function checkNextConfig() {
  console.log('\n⚡ Checking Next.js configuration...');
  
  try {
    if (fs.existsSync('next.config.js') || fs.existsSync('next.config.mjs')) {
      console.log('✅ Next.js config found');
      return true;
    } else {
      console.log('⚠️  No Next.js config found (this is optional)');
      return true;
    }
  } catch (error) {
    console.log('❌ Could not check Next.js config');
    return false;
  }
}

// Main execution
async function main() {
  const checks = [
    checkProblematicImports(),
    checkSyntaxErrors(),
    checkDependencies(),
    checkTypeScriptConfig(),
    checkNextConfig()
  ];
  
  const allPassed = checks.every(check => check);
  
  console.log('\n' + '='.repeat(50));
  
  if (allPassed) {
    console.log('🎉 All checks passed! Build should succeed.');
    console.log('\n💡 To build the project, run:');
    console.log('   pnpm build');
    process.exit(0);
  } else {
    console.log('❌ Some issues found. Please fix them before building.');
    console.log('\n🔧 Common fixes:');
    console.log('   - Replace FilePdf with File in lucide-react imports');
    console.log('   - Install missing dependencies with: pnpm install');
    console.log('   - Check TypeScript configuration');
    process.exit(1);
  }
}

// Run the checks
main().catch(error => {
  console.error('Error running build checks:', error);
  process.exit(1);
});
