#!/bin/bash

# This script resets the migration state in the database
# Use this if migrations are failing due to constraint issues

# Color codes for terminal output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Check if we need to use sudo for Docker commands
if [ -z "$DOCKER" ]; then
  if sudo docker info &> /dev/null; then
    echo -e "${YELLOW}Docker requires sudo privileges. Using sudo for Docker commands.${NC}"
    DOCKER="sudo docker"
    DOCKER_COMPOSE="sudo docker compose"
  elif docker info &> /dev/null; then
    DOCKER="docker"
    DOCKER_COMPOSE="docker compose"
  else
    echo -e "${RED}Cannot access Docker daemon. Please check Docker installation or permissions.${NC}"
    echo -e "${YELLOW}Try running this script with sudo or add your user to the docker group:${NC}"
    echo -e "  sudo usermod -aG docker $USER && newgrp docker"
    exit 1
  fi
fi

echo -e "${BLUE}Resetting migration state in the database...${NC}"

# SQL script to reset migration state
SQL_SCRIPT=$(cat <<EOF
-- Reset migration state
BEGIN;

-- First, check if the _prisma_migrations table exists
DO \$\$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = '_prisma_migrations'
    ) THEN
        -- Delete problematic migrations
        DELETE FROM _prisma_migrations 
        WHERE migration_name LIKE '%deploy_migration%' 
        OR migration_name = '20250515055114_init';
        
        RAISE NOTICE 'Deleted problematic migrations from _prisma_migrations table';
    ELSE
        RAISE NOTICE '_prisma_migrations table does not exist';
    END IF;
END \$\$;

COMMIT;
EOF
)

# Execute the SQL script in the database container
echo -e "${BLUE}Executing SQL reset...${NC}"
$DOCKER_COMPOSE exec -T db psql -U postgres -d carbon_exchange -c "$SQL_SCRIPT"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}Migration state reset successfully!${NC}"
else
    echo -e "${RED}Failed to reset migration state.${NC}"
    exit 1
fi

echo -e "${BLUE}Now try running the deployment script again:${NC}"
echo -e "  ./scripts/carbonx-deploy.sh"
