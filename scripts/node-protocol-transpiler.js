#!/usr/bin/env node

/**
 * This script transpiles node: protocol imports to regular imports.
 * This is needed for compatibility with certain environments.
 */

const fs = require('fs');
const path = require('path');

// Define the source directories to scan
const sourceDirectories = [
  path.join(process.cwd(), 'src'),
];

// Define specific node_modules files to process
const specificNodeModulesFiles = [
  // Add specific node_modules files here if needed
];

// Define file extensions to process
const fileExtensions = ['.js', '.jsx', '.ts', '.tsx'];

// Counter for modified files
let modifiedFilesCount = 0;

// Function to process a file
function processFile(filePath) {
  // Read the file content
  const content = fs.readFileSync(filePath, 'utf8');

  // Check if the file contains node: protocol imports
  if (content.includes('node:')) {
    // Replace node: protocol imports with regular imports
    const newContent = content.replace(/from ['"]node:([^'"]+)['"]/g, 'from \'$1\'');

    // If the content has changed, write it back to the file
    if (newContent !== content) {
      fs.writeFileSync(filePath, newContent, 'utf8');
      modifiedFilesCount++;
      return true;
    }
  }

  return false;
}

// Function to recursively scan a directory
function scanDirectory(directory) {
  // Check if directory exists before trying to read it
  if (!fs.existsSync(directory)) {
    console.log(`Directory does not exist, skipping: ${directory}`);
    return;
  }

  const files = fs.readdirSync(directory);

  for (const file of files) {
    const filePath = path.join(directory, file);

    // Check if file exists before trying to get stats
    if (!fs.existsSync(filePath)) {
      console.log(`File does not exist, skipping: ${filePath}`);
      continue;
    }

    const stats = fs.statSync(filePath);

    if (stats.isDirectory()) {
      // Skip node_modules and .next directories
      if (file !== 'node_modules' && file !== '.next') {
        scanDirectory(filePath);
      }
    } else if (stats.isFile() && fileExtensions.includes(path.extname(file))) {
      processFile(filePath);
    }
  }
}

// Main function
function main() {
  console.log('Transpiling node: protocol imports...');

  // Process source directories
  for (const directory of sourceDirectories) {
    if (fs.existsSync(directory)) {
      scanDirectory(directory);
    }
  }

  console.log(`Transpiled node: protocol imports in ${modifiedFilesCount} source files`);

  // Process specific node_modules files
  console.log('Processing specific node_modules files...');
  let specificFilesModified = 0;

  for (const filePath of specificNodeModulesFiles) {
    const fullPath = path.join(process.cwd(), filePath);
    if (fs.existsSync(fullPath)) {
      if (processFile(fullPath)) {
        specificFilesModified++;
      }
    }
  }

  console.log(`Transpiled node: protocol imports in ${specificFilesModified} specific node_modules files`);
  console.log(`Total files modified: ${modifiedFilesCount + specificFilesModified}`);
}

// Run the main function
main();
