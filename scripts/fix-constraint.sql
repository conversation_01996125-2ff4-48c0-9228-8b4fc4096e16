-- Fix for Transaction_parentValuation_fkey constraint issue
-- Run this script directly in the database if migrations are still failing

-- Check and fix constraints
DO $$
BEGIN
    -- Check if Transaction_parentValuation_fkey already exists
    IF EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = 'Transaction_parentValuation_fkey'
    ) THEN
        -- If it exists, we don't need to do anything
        RAISE NOTICE 'Constraint Transaction_parentValuation_fkey already exists';
    ELSE
        -- If it doesn't exist, check if Transaction_assetValuationId_fkey exists
        IF EXISTS (
            SELECT 1 FROM pg_constraint 
            WHERE conname = 'Transaction_assetValuationId_fkey'
        ) THEN
            -- Rename the constraint
            ALTER TABLE "Transaction" RENAME CONSTRAINT "Transaction_assetValuationId_fkey" TO "Transaction_parentValuation_fkey";
            RAISE NOTICE 'Renamed constraint Transaction_assetValuationId_fkey to Transaction_parentValuation_fkey';
        ELSE
            RAISE NOTICE 'Neither constraint exists, no action needed';
        END IF;
    END IF;
    
    -- Additional fix: Check if the Transaction table has the correct columns
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'Transaction' AND column_name = 'assetValuationId'
    ) THEN
        RAISE NOTICE 'Transaction table has assetValuationId column';
        
        -- Make sure the foreign key references are correct
        -- This is a more aggressive fix that might be needed in some cases
        BEGIN
            -- Drop the constraint if it exists (will be recreated properly)
            IF EXISTS (
                SELECT 1 FROM pg_constraint 
                WHERE conname = 'Transaction_parentValuation_fkey'
            ) THEN
                ALTER TABLE "Transaction" DROP CONSTRAINT "Transaction_parentValuation_fkey";
                RAISE NOTICE 'Dropped constraint Transaction_parentValuation_fkey';
            END IF;
            
            -- Create the constraint with the correct name
            ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_parentValuation_fkey" 
                FOREIGN KEY ("assetValuationId") REFERENCES "AssetValuation"(id);
            RAISE NOTICE 'Created constraint Transaction_parentValuation_fkey';
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Error fixing foreign key: %', SQLERRM;
        END;
    END IF;
END $$;
