#!/usr/bin/env node

/**
 * SPV Implementation Validation Script
 * 
 * This script validates that all SPV verification workflow components
 * are properly implemented and integrated.
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Validating SPV Verification Workflow Implementation...\n');

// Define required files and their purposes
const requiredFiles = [
  // Database Schema
  {
    path: 'prisma/schema.prisma',
    description: 'Database schema with SPV and SPVDocument models',
    checks: ['model SPV', 'model SPVDocument', 'enum SPVVerificationStatus', 'enum SPVDocumentType']
  },
  
  // Types
  {
    path: 'src/types/spv.ts',
    description: 'SPV TypeScript interfaces',
    checks: ['SPVVerificationStatus', 'SPVDocumentType', 'SPVCreateData', 'SPVVerificationData']
  },
  
  // Validation Schemas
  {
    path: 'src/lib/validation/schemas.ts',
    description: 'SPV validation schemas',
    checks: ['spvCreationSchema', 'spvVerificationSchema']
  },
  
  // API Routes
  {
    path: 'src/app/api/organizations/spvs/route.ts',
    description: 'SPV CRUD API',
    checks: ['POST', 'GET', 'verificationStatus']
  },
  {
    path: 'src/app/api/organizations/spvs/[id]/verification/route.ts',
    description: 'SPV verification API',
    checks: ['GET', 'PUT', 'spvVerificationSchema']
  },
  {
    path: 'src/app/api/organizations/spvs/[id]/verification/actions/route.ts',
    description: 'SPV verification actions API',
    checks: ['POST', 'APPROVE', 'REJECT', 'REQUEST_MORE_INFO']
  },
  {
    path: 'src/app/api/organizations/spvs/[id]/documents/route.ts',
    description: 'SPV document management API',
    checks: ['GET', 'POST', 'SPVDocument']
  },
  {
    path: 'src/app/api/admin/spv-verification/route.ts',
    description: 'Admin verification queue API',
    checks: ['GET', 'verificationStatus', 'statistics']
  },
  
  // Components
  {
    path: 'src/components/spv/spv-creation-form.tsx',
    description: 'Reusable SPV creation form',
    checks: ['SPVCreationForm', 'spvCreationSchema', 'mode']
  },
  {
    path: 'src/components/spv/spv-verification-form.tsx',
    description: 'SPV verification details form',
    checks: ['SPVVerificationForm', 'spvVerificationSchema', 'Tabs']
  },
  {
    path: 'src/components/spv/spv-document-upload.tsx',
    description: 'Document upload component',
    checks: ['SPVDocumentUpload', 'useDropzone', 'documentTypes']
  },
  {
    path: 'src/components/spv/spv-document-viewer.tsx',
    description: 'Document viewer and verification',
    checks: ['SPVDocumentViewer', 'onVerifyDocument', 'documentTypeLabels']
  },
  {
    path: 'src/components/spv/spv-onboarding-stepper.tsx',
    description: 'Multi-step onboarding process',
    checks: ['SPVOnboardingStepper', 'currentStep', 'Progress']
  },
  {
    path: 'src/components/admin/spv-verification-dashboard.tsx',
    description: 'Admin verification dashboard',
    checks: ['SPVVerificationDashboard', 'verificationStatus', 'statistics']
  },
  {
    path: 'src/components/admin/spv-verification-details.tsx',
    description: 'Admin verification details page',
    checks: ['SPVVerificationDetails', 'handleVerificationAction', 'Tabs']
  },
  
  // Authorization
  {
    path: 'src/lib/authorization.ts',
    description: 'Updated authorization with SPV permissions',
    checks: ['hasSPVPermission', 'canVerifySPV', 'VERIFY_SPV']
  },
  
  // Email Service
  {
    path: 'src/lib/email/email-service.ts',
    description: 'Email notifications for SPV workflow',
    checks: ['sendSPVVerificationStatusEmail', 'sendSPVDocumentVerificationEmail']
  },
  
  // File Upload
  {
    path: 'src/lib/file-upload.ts',
    description: 'File upload utilities',
    checks: ['uploadFile', 'validateFile', 'deleteFile']
  },
  
  // Updated Components
  {
    path: 'src/components/spv/spv-card-grid.tsx',
    description: 'Updated SPV card with verification status',
    checks: ['getVerificationStatusStyle', 'verificationStatus', 'Shield']
  },
  {
    path: 'src/components/spv/spv-filters.tsx',
    description: 'Updated SPV stats with verification counts',
    checks: ['verifiedSPVs', 'pendingVerificationSPVs', 'rejectedSPVs']
  },
  {
    path: 'src/app/dashboard/spv/spv-management-client.tsx',
    description: 'Updated SPV management with new forms',
    checks: ['SPVCreationForm', 'SPVVerificationForm', 'handleVerificationSubmit']
  }
];

// Documentation files
const documentationFiles = [
  {
    path: 'docs/spv-workflow-testing-guide.md',
    description: 'Manual testing guide'
  },
  {
    path: 'docs/spv-verification-implementation-summary.md',
    description: 'Implementation summary'
  }
];

// Test files
const testFiles = [
  {
    path: 'src/tests/spv-workflow-integration.test.ts',
    description: 'Integration tests for SPV workflow'
  }
];

let totalChecks = 0;
let passedChecks = 0;
let failedChecks = [];

function checkFile(fileInfo) {
  const filePath = path.join(process.cwd(), fileInfo.path);
  
  console.log(`📁 Checking: ${fileInfo.description}`);
  console.log(`   Path: ${fileInfo.path}`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`   ❌ File not found`);
    failedChecks.push(`${fileInfo.path} - File not found`);
    totalChecks++;
    return;
  }
  
  console.log(`   ✅ File exists`);
  totalChecks++;
  passedChecks++;
  
  if (fileInfo.checks) {
    const content = fs.readFileSync(filePath, 'utf8');
    
    fileInfo.checks.forEach(check => {
      totalChecks++;
      if (content.includes(check)) {
        console.log(`   ✅ Contains: ${check}`);
        passedChecks++;
      } else {
        console.log(`   ❌ Missing: ${check}`);
        failedChecks.push(`${fileInfo.path} - Missing: ${check}`);
      }
    });
  }
  
  console.log('');
}

// Check all required files
console.log('🔧 Checking Core Implementation Files...\n');
requiredFiles.forEach(checkFile);

console.log('📚 Checking Documentation Files...\n');
documentationFiles.forEach(checkFile);

console.log('🧪 Checking Test Files...\n');
testFiles.forEach(checkFile);

// Additional integration checks
console.log('🔗 Checking Integration Points...\n');

// Check if database schema is properly structured
const schemaPath = path.join(process.cwd(), 'prisma/schema.prisma');
if (fs.existsSync(schemaPath)) {
  const schema = fs.readFileSync(schemaPath, 'utf8');
  
  console.log('📊 Database Schema Validation:');
  
  // Check SPV model has verification fields
  const spvModelMatch = schema.match(/model SPV \{([\s\S]*?)\}/);
  if (spvModelMatch) {
    const spvModel = spvModelMatch[1];
    const verificationFields = [
      'verificationStatus',
      'gstNumber',
      'cinNumber',
      'panNumber',
      'contactPersonEmail',
      'bankAccountNumber'
    ];
    
    verificationFields.forEach(field => {
      totalChecks++;
      if (spvModel.includes(field)) {
        console.log(`   ✅ SPV model has ${field}`);
        passedChecks++;
      } else {
        console.log(`   ❌ SPV model missing ${field}`);
        failedChecks.push(`SPV model missing ${field}`);
      }
    });
  }
  
  // Check SPVDocument model exists
  totalChecks++;
  if (schema.includes('model SPVDocument')) {
    console.log(`   ✅ SPVDocument model exists`);
    passedChecks++;
  } else {
    console.log(`   ❌ SPVDocument model missing`);
    failedChecks.push('SPVDocument model missing');
  }
  
  console.log('');
}

// Summary
console.log('📋 Validation Summary\n');
console.log(`Total Checks: ${totalChecks}`);
console.log(`Passed: ${passedChecks}`);
console.log(`Failed: ${totalChecks - passedChecks}`);
console.log(`Success Rate: ${Math.round((passedChecks / totalChecks) * 100)}%\n`);

if (failedChecks.length > 0) {
  console.log('❌ Failed Checks:');
  failedChecks.forEach(check => {
    console.log(`   - ${check}`);
  });
  console.log('');
}

if (passedChecks === totalChecks) {
  console.log('🎉 All checks passed! SPV verification workflow is properly implemented.');
  process.exit(0);
} else {
  console.log('⚠️  Some checks failed. Please review the implementation.');
  process.exit(1);
}
