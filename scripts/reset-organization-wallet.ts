import { db as prisma } from '../src/lib/db';

async function main() {
  const organizationId = 'cm9mx0oy0000cldev5h2h8huj'; // Replace with your organization ID

  console.log(`Checking for existing wallets for organization: ${organizationId}`);

  // Find existing wallets
  const existingWallets = await prisma.wallet.findMany({
    where: {
      organizationId
    }
  });

  if (existingWallets.length === 0) {
    console.log('No wallets found for this organization.');
    return;
  }

  console.log(`Found ${existingWallets.length} wallets. Deleting...`);

  // Delete all wallets for this organization
  const deleteResult = await prisma.wallet.deleteMany({
    where: {
      organizationId
    }
  });

  console.log(`Deleted ${deleteResult.count} wallets.`);

  console.log('Organization wallet reset complete!');
}

main()
  .catch((e) => {
    console.error('Error resetting organization wallet:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
