import { OrderStatus, TransactionType, TransactionStatus } from '@prisma/client';
import { db as prisma } from '../src/lib/db';

async function main() {
  console.log('Creating completed transaction...');

  // Find a pending buy order
  const order = await prisma.order.findFirst({
    where: {
      status: OrderStatus.PENDING,
      type: 'BUY'
    },
    include: {
      buyer: {
        include: {
          wallets: true
        }
      },
      seller: {
        include: {
          wallets: true
        }
      },
      carbonCredit: true
    }
  });

  if (!order) {
    console.error('No pending buy order found.');
    return;
  }

  console.log(`Found order: ${order.id} for ${order.quantity} tons of ${order.carbonCredit.name}`);

  // Check if buyer and seller have wallets
  if (!order.buyer.wallets.length || !order.seller.wallets.length) {
    console.log('Creating wallets for buyer and seller...');

    // Create wallets if they don't exist
    if (!order.buyer.wallets.length) {
      await prisma.wallet.create({
        data: {
          address: `0x${Math.random().toString(16).substring(2, 42)}`,
          network: 'ethereum',
          chainId: 1,
          balance: 10000,
          user: {
            connect: {
              id: order.buyer.id
            }
          },
          organization: {
            connect: {
              id: order.buyer.organizationId
            }
          }
        }
      });
      console.log(`Created wallet for buyer: ${order.buyer.email}`);
    }

    if (!order.seller.wallets.length) {
      await prisma.wallet.create({
        data: {
          address: `0x${Math.random().toString(16).substring(2, 42)}`,
          network: 'ethereum',
          chainId: 1,
          balance: 5000,
          user: {
            connect: {
              id: order.seller.id
            }
          },
          organization: {
            connect: {
              id: order.seller.organizationId
            }
          }
        }
      });
      console.log(`Created wallet for seller: ${order.seller.email}`);
    }

    // Refresh order with wallets
    order.buyer = await prisma.user.findUnique({
      where: { id: order.buyer.id },
      include: { wallets: true }
    });

    order.seller = await prisma.user.findUnique({
      where: { id: order.seller.id },
      include: { wallets: true }
    });
  }

  const buyerWallet = order.buyer.wallets[0];
  const sellerWallet = order.seller.wallets[0];

  console.log(`Using buyer wallet: ${buyerWallet.address}`);
  console.log(`Using seller wallet: ${sellerWallet.address}`);

  // Update order status to COMPLETED
  const updatedOrder = await prisma.order.update({
    where: {
      id: order.id
    },
    data: {
      status: OrderStatus.COMPLETED
    }
  });

  console.log(`Updated order status to: ${updatedOrder.status}`);

  // Create transaction for the buyer (PURCHASE)
  const buyerTransaction = await prisma.transaction.create({
    data: {
      amount: order.quantity * order.price,
      fee: order.quantity * order.price * 0.01, // 1% fee
      type: TransactionType.PURCHASE,
      status: TransactionStatus.COMPLETED,
      transactionHash: `0x${Math.random().toString(16).substring(2, 66)}`,
      blockNumber: Math.floor(Math.random() * 1000000) + 15000000,
      network: 'ethereum',
      chainId: 1,
      wallet: {
        connect: {
          id: buyerWallet.id
        }
      },
      order: {
        connect: {
          id: order.id
        }
      }
    }
  });

  console.log(`Created buyer transaction: ${buyerTransaction.id} for $${buyerTransaction.amount}`);

  // Create transaction for the seller (SALE)
  const sellerTransaction = await prisma.transaction.create({
    data: {
      amount: order.quantity * order.price,
      fee: order.quantity * order.price * 0.01, // 1% fee
      type: TransactionType.SALE,
      status: TransactionStatus.COMPLETED,
      transactionHash: `0x${Math.random().toString(16).substring(2, 66)}`,
      blockNumber: Math.floor(Math.random() * 1000000) + 15000000,
      network: 'ethereum',
      chainId: 1,
      wallet: {
        connect: {
          id: sellerWallet.id
        }
      },
      order: {
        connect: {
          id: order.id
        }
      }
    }
  });

  console.log(`Created seller transaction: ${sellerTransaction.id} for $${sellerTransaction.amount}`);

  // Update carbon credit available quantity
  const updatedCarbonCredit = await prisma.carbonCredit.update({
    where: {
      id: order.carbonCredit.id
    },
    data: {
      availableQuantity: {
        decrement: order.quantity
      }
    }
  });

  console.log(`Updated carbon credit available quantity to: ${updatedCarbonCredit.availableQuantity}`);
  console.log('Completed transaction created successfully!');
}

main()
  .catch((e) => {
    console.error('Error creating completed transaction:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
