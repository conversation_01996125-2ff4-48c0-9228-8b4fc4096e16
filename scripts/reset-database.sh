#!/bin/bash

# This script completely resets the database
# Use this as a last resort if migrations are still failing

# Color codes for terminal output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Check if we need to use sudo for Docker commands
if [ -z "$DOCKER" ]; then
  if sudo docker info &> /dev/null; then
    echo -e "${YELLOW}Docker requires sudo privileges. Using sudo for Docker commands.${NC}"
    DOCKER="sudo docker"
    DOCKER_COMPOSE="sudo docker compose"
  elif docker info &> /dev/null; then
    DOCKER="docker"
    DOCKER_COMPOSE="docker compose"
  else
    echo -e "${RED}Cannot access Docker daemon. Please check Docker installation or permissions.${NC}"
    echo -e "${YELLOW}Try running this script with sudo or add your user to the docker group:${NC}"
    echo -e "  sudo usermod -aG docker $USER && newgrp docker"
    exit 1
  fi
fi

echo -e "${RED}WARNING: This will completely reset your database!${NC}"
echo -e "${YELLOW}All data will be lost. This should only be used as a last resort.${NC}"
read -p "Are you sure you want to continue? (y/n): " CONFIRM

if [[ "$CONFIRM" != "y" && "$CONFIRM" != "Y" ]]; then
    echo -e "${BLUE}Database reset cancelled.${NC}"
    exit 0
fi

echo -e "${BLUE}Stopping the database container...${NC}"
$DOCKER_COMPOSE down db

echo -e "${BLUE}Removing database volumes...${NC}"
$DOCKER volume rm carbonx_postgres_data

echo -e "${BLUE}Starting a fresh database container...${NC}"
$DOCKER_COMPOSE up -d db

echo -e "${BLUE}Waiting for PostgreSQL to be ready...${NC}"
until $DOCKER_COMPOSE exec db pg_isready -U postgres; do
  echo -e "${YELLOW}PostgreSQL is not ready yet... waiting${NC}"
  sleep 2
done

echo -e "${GREEN}Database has been completely reset!${NC}"
echo -e "${BLUE}Now try running the deployment script again:${NC}"
echo -e "  ./scripts/carbonx-deploy.sh"
