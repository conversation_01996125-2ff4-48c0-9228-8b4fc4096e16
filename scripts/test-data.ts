import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('Testing database data...');

  // Check admin user
  const adminUser = await prisma.user.findFirst({
    where: {
      email: '<EMAIL>'
    },
    include: {
      wallets: true,
      organization: true
    }
  });

  console.log('\n=== Admin User ===');
  console.log(`Admin user: ${adminUser?.name} (${adminUser?.email})`);
  console.log(`Organization: ${adminUser?.organization?.name}`);
  console.log(`Role: ${adminUser?.role}`);
  console.log(`Wallets: ${adminUser?.wallets?.length || 0}`);

  // Check total transactions
  const totalTransactions = await prisma.transaction.count();
  console.log(`\n=== Total Transactions ===`);
  console.log(`Total transactions in database: ${totalTransactions}`);

  // Check transactions by type
  const transactionsByType = await prisma.transaction.groupBy({
    by: ['type'],
    _count: {
      id: true
    }
  });

  console.log('\n=== Transactions by Type ===');
  transactionsByType.forEach(group => {
    console.log(`${group.type}: ${group._count.id}`);
  });

  // Check transactions with orders vs without orders
  const transactionsWithOrders = await prisma.transaction.count({
    where: {
      orderId: {
        not: null
      }
    }
  });

  const transactionsWithoutOrders = await prisma.transaction.count({
    where: {
      orderId: null
    }
  });

  console.log('\n=== Transactions by Order Association ===');
  console.log(`Transactions with orders: ${transactionsWithOrders}`);
  console.log(`Transactions without orders: ${transactionsWithoutOrders}`);

  // Check recent transactions
  const recentTransactions = await prisma.transaction.findMany({
    take: 5,
    orderBy: {
      createdAt: 'desc'
    },
    include: {
      wallet: {
        select: {
          id: true,
          name: true,
          userId: true,
          organizationId: true
        }
      },
      order: {
        select: {
          id: true,
          type: true,
          status: true
        }
      }
    }
  });

  console.log('\n=== Recent Transactions ===');
  recentTransactions.forEach(tx => {
    console.log(`${tx.id}: ${tx.type} - $${tx.amount} (Wallet: ${tx.wallet.name}, Order: ${tx.orderId ? 'Yes' : 'No'})`);
  });

  // Check notifications
  const totalNotifications = await prisma.notification.count();
  console.log(`\n=== Notifications ===`);
  console.log(`Total notifications: ${totalNotifications}`);

  // Check notifications for admin user
  const adminNotifications = await prisma.notification.count({
    where: {
      userId: adminUser?.id
    }
  });

  console.log(`Admin user notifications: ${adminNotifications}`);

  // Check recent notifications
  const recentNotifications = await prisma.notification.findMany({
    take: 5,
    orderBy: {
      createdAt: 'desc'
    },
    include: {
      user: {
        select: {
          name: true,
          email: true
        }
      }
    }
  });

  console.log('\n=== Recent Notifications ===');
  recentNotifications.forEach(notif => {
    console.log(`${notif.id}: ${notif.title} (User: ${notif.user.name})`);
  });

  await prisma.$disconnect();
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });
