#!/usr/bin/env tsx

/**
 * <PERSON><PERSON>t to analyze project assignment conflicts
 * This script identifies projects that are assigned to users from different SPVs
 * 
 * Run with: npx tsx scripts/analyze-assignment-conflicts.ts
 */

import { PrismaClient } from '@prisma/client';

const db = new PrismaClient();

async function main() {
  console.log('🔍 Analyzing project assignment conflicts...');

  try {
    // Get all project assignments with SPV information
    const assignments = await db.projectAssignment.findMany({
      where: {
        isActive: true,
      },
      include: {
        project: {
          select: {
            id: true,
            name: true,
            spvId: true,
            organizationId: true,
          },
        },
        spvUser: {
          include: {
            spv: {
              select: {
                id: true,
                name: true,
                organizationId: true,
              },
            },
          },
        },
      },
    });

    console.log(`📊 Found ${assignments.length} total project assignments`);

    // Group assignments by project
    const projectAssignments = new Map<string, typeof assignments>();
    
    for (const assignment of assignments) {
      const projectId = assignment.project.id;
      if (!projectAssignments.has(projectId)) {
        projectAssignments.set(projectId, []);
      }
      projectAssignments.get(projectId)!.push(assignment);
    }

    console.log(`📋 Projects with assignments: ${projectAssignments.size}`);

    // Find conflicts
    const conflicts: Array<{
      projectId: string;
      projectName: string;
      currentSpvId: string | null;
      assignedSpvs: Array<{ spvId: string; spvName: string; userCount: number }>;
    }> = [];

    for (const [projectId, assignmentsForProject] of projectAssignments.entries()) {
      const project = assignmentsForProject[0].project;
      const spvGroups = new Map<string, { spvName: string; userCount: number }>();

      // Group by SPV
      for (const assignment of assignmentsForProject) {
        const spvId = assignment.spvUser.spvId;
        const spvName = assignment.spvUser.spv.name;
        
        if (!spvGroups.has(spvId)) {
          spvGroups.set(spvId, { spvName, userCount: 0 });
        }
        spvGroups.get(spvId)!.userCount++;
      }

      // Check for conflicts (project assigned to multiple SPVs)
      if (spvGroups.size > 1) {
        conflicts.push({
          projectId: project.id,
          projectName: project.name,
          currentSpvId: project.spvId,
          assignedSpvs: Array.from(spvGroups.entries()).map(([spvId, info]) => ({
            spvId,
            spvName: info.spvName,
            userCount: info.userCount,
          })),
        });
      }
    }

    console.log(`\n⚠️  Found ${conflicts.length} projects with assignment conflicts:`);

    for (const conflict of conflicts) {
      console.log(`\n📋 Project: "${conflict.projectName}" (ID: ${conflict.projectId})`);
      console.log(`   Current spvId: ${conflict.currentSpvId}`);
      console.log(`   Assigned to ${conflict.assignedSpvs.length} different SPVs:`);
      
      for (const spv of conflict.assignedSpvs) {
        const isCurrentSpv = spv.spvId === conflict.currentSpvId;
        console.log(`     • ${spv.spvName} (${spv.spvId}) - ${spv.userCount} users ${isCurrentSpv ? '✅ CURRENT' : '❌ CONFLICT'}`);
      }

      // Suggest resolution
      const majoritySpv = conflict.assignedSpvs.reduce((max, current) => 
        current.userCount > max.userCount ? current : max
      );
      
      console.log(`   💡 Suggested resolution: Set spvId to ${majoritySpv.spvId} (${majoritySpv.spvName}) - has ${majoritySpv.userCount} users`);
    }

    // Check for projects with no assignments
    const projectsWithoutAssignments = await db.project.findMany({
      where: {
        spvAssignments: {
          none: {
            isActive: true,
          },
        },
        spvId: {
          not: null,
        },
      },
      select: {
        id: true,
        name: true,
        spvId: true,
      },
    });

    if (projectsWithoutAssignments.length > 0) {
      console.log(`\n📋 Projects with spvId but no active assignments: ${projectsWithoutAssignments.length}`);
      for (const project of projectsWithoutAssignments) {
        console.log(`   • "${project.name}" (spvId: ${project.spvId})`);
      }
    }

    console.log('\n📈 Summary:');
    console.log(`   • Total assignments: ${assignments.length}`);
    console.log(`   • Projects with assignments: ${projectAssignments.size}`);
    console.log(`   • Conflicted projects: ${conflicts.length}`);
    console.log(`   • Projects with spvId but no assignments: ${projectsWithoutAssignments.length}`);

  } catch (error) {
    console.error('❌ Analysis failed:', error);
    throw error;
  } finally {
    await db.$disconnect();
  }
}

// Run the analysis
main()
  .catch((error) => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
