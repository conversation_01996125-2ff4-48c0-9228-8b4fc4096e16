#!/usr/bin/env tsx

/**
 * Check UserRole enum in database vs schema
 */

import { PrismaClient } from '@prisma/client';

const db = new PrismaClient();

async function main() {
  console.log('🔍 Checking UserRole enum...');

  try {
    // Check current UserRole enum values in database
    const currentUserRoles = await db.$queryRaw`
      SELECT enumlabel 
      FROM pg_enum 
      WHERE enumtypid = (
        SELECT oid 
        FROM pg_type 
        WHERE typname = 'UserRole'
      )
      ORDER BY enumlabel;
    `;
    
    console.log('\n📋 Current UserRole enum values in database:');
    (currentUserRoles as any[]).forEach((role: any) => {
      console.log(`  • ${role.enumlabel}`);
    });

    // Check if any users have the 'USER' role
    const usersWithUserRole = await db.$queryRaw`
      SELECT id, email, role 
      FROM "User" 
      WHERE role = 'USER'
      LIMIT 5;
    `;
    
    if ((usersWithUserRole as any[]).length > 0) {
      console.log('\n⚠️  Users with USER role found:');
      (usersWithUserRole as any[]).forEach((user: any) => {
        console.log(`  • ${user.email} (${user.id})`);
      });
    } else {
      console.log('\n✅ No users with USER role found');
    }

    // Check all user roles in use
    const rolesInUse = await db.$queryRaw`
      SELECT role, COUNT(*) as count
      FROM "User" 
      GROUP BY role
      ORDER BY count DESC;
    `;
    
    console.log('\n📊 User roles currently in use:');
    (rolesInUse as any[]).forEach((roleCount: any) => {
      console.log(`  • ${roleCount.role}: ${roleCount.count} users`);
    });

  } catch (error) {
    console.error('❌ Error checking UserRole enum:', error);
    throw error;
  } finally {
    await db.$disconnect();
  }
}

// Run the check
main()
  .catch((error) => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
