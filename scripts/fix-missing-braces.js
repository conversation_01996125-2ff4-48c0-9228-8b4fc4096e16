#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing missing closing braces in React components...\n');

// Files that need fixing based on the error output
const filesToFix = [
  'src/components/compliance/compliance-dashboard.tsx',
  'src/components/compliance/report-generation-wizard.tsx',
  'src/components/compliance/report-management.tsx',
  'src/components/impact/impact-dashboard.tsx',
  'src/components/projects/project-portfolio-dashboard.tsx',
];

function fixMissingBrace(filePath) {
  try {
    console.log(`🔍 Checking ${filePath}...`);
    
    if (!fs.existsSync(filePath)) {
      console.log(`❌ File not found: ${filePath}`);
      return false;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    
    // Check if file ends with ); instead of ); }
    if (content.trim().endsWith(');')) {
      const lines = content.split('\n');
      const lastNonEmptyLineIndex = lines.length - 1;
      
      // Find the last non-empty line
      let lastLineIndex = lastNonEmptyLineIndex;
      while (lastLineIndex >= 0 && lines[lastLineIndex].trim() === '') {
        lastLineIndex--;
      }
      
      if (lastLineIndex >= 0 && lines[lastLineIndex].trim() === ');') {
        // Replace the last ); with ); }
        lines[lastLineIndex] = lines[lastLineIndex].replace(');', '); }');
        
        const newContent = lines.join('\n');
        fs.writeFileSync(filePath, newContent, 'utf8');
        
        console.log(`✅ Fixed ${filePath} - Added missing closing brace`);
        return true;
      }
    }
    
    console.log(`ℹ️  ${filePath} - No fix needed`);
    return true;
    
  } catch (error) {
    console.log(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Fix all files
let fixedCount = 0;
let errorCount = 0;

filesToFix.forEach(filePath => {
  const fullPath = path.join(process.cwd(), filePath);
  const success = fixMissingBrace(fullPath);
  
  if (success) {
    fixedCount++;
  } else {
    errorCount++;
  }
});

console.log('\n' + '='.repeat(50));
console.log(`🎉 Completed! Fixed ${fixedCount} files, ${errorCount} errors`);

if (errorCount === 0) {
  console.log('\n✅ All files processed successfully!');
  console.log('💡 You can now run: pnpm build');
} else {
  console.log('\n⚠️  Some files had errors. Please check them manually.');
  process.exit(1);
}
