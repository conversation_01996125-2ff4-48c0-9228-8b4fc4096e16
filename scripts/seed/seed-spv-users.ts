import { PrismaClient, SPVUserRole, UserRole } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

export async function seedSPVUsers(organizations: any[]) {
  console.log('Starting to seed SPV users and related data...');

  const spvUsers = [];
  const spvs = [];
  const projects = [];
  const projectAssignments = [];

  try {
    // Get the first organization for our SPV setup
    const organization = organizations[0];
    if (!organization) {
      console.log('No organizations found. Skipping SPV user seeding.');
      return { spvUsers: [], spvs: [], projects: [], projectAssignments: [] };
    }

    console.log(`Using organization: ${organization.name} for SPV setup`);

    // 1. Create SPVs
    const spvData = [
      {
        name: 'Solar Energy SPV',
        purpose: 'Solar power project development and management',
        jurisdiction: 'Delaware, USA',
        legalStructure: 'Limited Liability Company',
        address: '789 Solar Avenue, Renewable City, RC 13579',
        description: 'Special purpose vehicle for solar energy projects',
        status: 'ACTIVE',
        organizationId: organization.id,
      },
      {
        name: 'Wind Power SPV',
        purpose: 'Wind energy project development and operations',
        jurisdiction: 'Texas, USA',
        legalStructure: 'Limited Partnership',
        address: '456 Wind Farm Road, Gusty Plains, GP 24680',
        description: 'Special purpose vehicle for wind energy projects',
        status: 'ACTIVE',
        organizationId: organization.id,
      },
    ];

    for (const spvInfo of spvData) {
      const existingSPV = await prisma.sPV.findFirst({
        where: { name: spvInfo.name, organizationId: organization.id }
      });

      if (!existingSPV) {
        const spv = await prisma.sPV.create({ data: spvInfo });
        spvs.push(spv);
        console.log(`Created SPV: ${spv.name}`);
      } else {
        spvs.push(existingSPV);
        console.log(`SPV already exists: ${existingSPV.name}`);
      }
    }

    // 2. Create Projects for SPVs
    const projectData = [
      {
        name: 'Solar Farm Alpha',
        description: 'Large-scale solar power generation facility in Arizona',
        type: 'RENEWABLE_ENERGY',
        status: 'ACTIVE',
        location: 'Arizona Desert, USA',
        country: 'United States',
        startDate: new Date('2024-01-01'),
        endDate: new Date('2025-12-31'),
        organizationId: organization.id,
        spvId: spvs[0]?.id,
      },
      {
        name: 'Wind Farm Beta',
        description: 'Offshore wind power generation facility',
        type: 'RENEWABLE_ENERGY',
        status: 'ACTIVE',
        location: 'Texas Coast, USA',
        country: 'United States',
        startDate: new Date('2024-03-01'),
        endDate: new Date('2026-02-28'),
        organizationId: organization.id,
        spvId: spvs[1]?.id,
      },
      {
        name: 'Solar Farm Gamma',
        description: 'Community solar project for residential areas',
        type: 'RENEWABLE_ENERGY',
        status: 'PENDING',
        location: 'California Central Valley, USA',
        country: 'United States',
        startDate: new Date('2024-06-01'),
        endDate: new Date('2025-05-31'),
        organizationId: organization.id,
        spvId: spvs[0]?.id,
      },
    ];

    for (const projectInfo of projectData) {
      if (projectInfo.spvId) {
        const existingProject = await prisma.project.findFirst({
          where: { name: projectInfo.name, organizationId: organization.id }
        });

        if (!existingProject) {
          const project = await prisma.project.create({ data: projectInfo });
          projects.push(project);
          console.log(`Created project: ${project.name}`);
        } else {
          projects.push(existingProject);
          console.log(`Project already exists: ${existingProject.name}`);
        }
      }
    }

    // 3. Create SPV Users
    const spvUserData = [
      // SPV Admin for Solar Energy SPV
      {
        email: '<EMAIL>',
        name: 'SPV Administrator',
        jobTitle: 'SPV Administrator',
        role: UserRole.SPV_USER,
        spvRole: SPVUserRole.SPV_ADMIN,
        spvId: spvs[0]?.id,
      },
      // Project Manager for Solar Energy SPV
      {
        email: '<EMAIL>',
        name: 'Solar Project Manager',
        jobTitle: 'Project Manager',
        role: UserRole.SPV_USER,
        spvRole: SPVUserRole.PROJECT_MANAGER,
        spvId: spvs[0]?.id,
      },
      // Site Worker for Solar Energy SPV
      {
        email: '<EMAIL>',
        name: 'Solar Site Worker',
        jobTitle: 'Site Worker',
        role: UserRole.SPV_USER,
        spvRole: SPVUserRole.SITE_WORKER,
        spvId: spvs[0]?.id,
      },
      // SPV Admin for Wind Power SPV
      {
        email: '<EMAIL>',
        name: 'Wind SPV Administrator',
        jobTitle: 'SPV Administrator',
        role: UserRole.SPV_USER,
        spvRole: SPVUserRole.SPV_ADMIN,
        spvId: spvs[1]?.id,
      },
      // Project Manager for Wind Power SPV
      {
        email: '<EMAIL>',
        name: 'Wind Project Manager',
        jobTitle: 'Project Manager',
        role: UserRole.SPV_USER,
        spvRole: SPVUserRole.PROJECT_MANAGER,
        spvId: spvs[1]?.id,
      },
      // Site Worker for Wind Power SPV
      {
        email: '<EMAIL>',
        name: 'Wind Site Worker',
        jobTitle: 'Site Worker',
        role: UserRole.SPV_USER,
        spvRole: SPVUserRole.SITE_WORKER,
        spvId: spvs[1]?.id,
      },
    ];

    const hashedPassword = await bcrypt.hash('SPVUser123!', 10);

    for (const userData of spvUserData) {
      if (userData.spvId) {
        // Check if user already exists
        const existingUser = await prisma.user.findUnique({
          where: { email: userData.email }
        });

        let user;
        if (!existingUser) {
          // Create the user
          user = await prisma.user.create({
            data: {
              email: userData.email,
              name: userData.name,
              password: hashedPassword,
              role: userData.role,
              organizationId: organization.id,
              emailVerified: new Date(),
              jobTitle: userData.jobTitle,
            },
          });
          console.log(`Created SPV user: ${user.email}`);
        } else {
          user = existingUser;
          console.log(`SPV user already exists: ${existingUser.email}`);
        }

        // Check if SPV user record already exists
        const existingSPVUser = await prisma.sPVUser.findFirst({
          where: { userId: user.id }
        });

        if (!existingSPVUser) {
          // Create the SPV user record
          const spvUser = await prisma.sPVUser.create({
            data: {
              userId: user.id,
              spvId: userData.spvId,
              role: userData.spvRole,
              isActive: true,
            },
          });
          spvUsers.push(spvUser);
          console.log(`Created SPV user record for: ${user.email} (${userData.spvRole})`);
        } else {
          spvUsers.push(existingSPVUser);
          console.log(`SPV user record already exists for: ${user.email}`);
        }
      }
    }

    // 4. Create Project Assignments
    // Get organization admin to assign projects
    const orgAdmin = await prisma.user.findFirst({
      where: {
        organizationId: organization.id,
        role: UserRole.ORGANIZATION_ADMIN,
      },
    });

    if (orgAdmin && spvUsers.length > 0 && projects.length > 0) {
      // Assign all SPV users to all projects for testing
      for (const spvUser of spvUsers) {
        for (const project of projects) {
          const existingAssignment = await prisma.projectAssignment.findFirst({
            where: {
              projectId: project.id,
              spvUserId: spvUser.id,
            },
          });

          if (!existingAssignment) {
            const assignment = await prisma.projectAssignment.create({
              data: {
                projectId: project.id,
                spvUserId: spvUser.id,
                assignedBy: orgAdmin.id,
                isActive: true,
              },
            });
            projectAssignments.push(assignment);
          } else {
            projectAssignments.push(existingAssignment);
          }
        }
      }
      console.log(`Created ${projectAssignments.length} project assignments`);
    }

    console.log('\n🎉 SPV Users seeding completed successfully!');
    console.log('\n📋 SPV PORTAL LOGIN CREDENTIALS:');
    console.log('==========================================');
    console.log('🏗️ SPV ADMIN (Solar):');
    console.log('   Email: <EMAIL>');
    console.log('   Password: SPVUser123!');
    console.log('');
    console.log('📊 PROJECT MANAGER (Solar):');
    console.log('   Email: <EMAIL>');
    console.log('   Password: SPVUser123!');
    console.log('');
    console.log('⚡ SITE WORKER (Solar):');
    console.log('   Email: <EMAIL>');
    console.log('   Password: SPVUser123!');
    console.log('');
    console.log('🏗️ SPV ADMIN (Wind):');
    console.log('   Email: <EMAIL>');
    console.log('   Password: SPVUser123!');
    console.log('');
    console.log('📊 PROJECT MANAGER (Wind):');
    console.log('   Email: <EMAIL>');
    console.log('   Password: SPVUser123!');
    console.log('');
    console.log('⚡ SITE WORKER (Wind):');
    console.log('   Email: <EMAIL>');
    console.log('   Password: SPVUser123!');
    console.log('==========================================\n');

    return { spvUsers, spvs, projects, projectAssignments };

  } catch (error) {
    console.error('Error seeding SPV users:', error);
    throw error;
  }
}
