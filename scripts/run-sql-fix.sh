#!/bin/bash

# This script runs the SQL fix directly in the database
# Use this if the deployment script is still failing

# Color codes for terminal output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Check if we need to use sudo for Docker commands
if [ -z "$DOCKER" ]; then
  if sudo docker info &> /dev/null; then
    echo -e "${YELLOW}Docker requires sudo privileges. Using sudo for Docker commands.${NC}"
    DOCKER="sudo docker"
    DOCKER_COMPOSE="sudo docker compose"
  elif docker info &> /dev/null; then
    DOCKER="docker"
    DOCKER_COMPOSE="docker compose"
  else
    echo -e "${RED}Cannot access Docker daemon. Please check Docker installation or permissions.${NC}"
    echo -e "${YELLOW}Try running this script with sudo or add your user to the docker group:${NC}"
    echo -e "  sudo usermod -aG docker $USER && newgrp docker"
    exit 1
  fi
fi

echo -e "${BLUE}Running SQL fix directly in the database...${NC}"

# Check if the SQL file exists
if [ ! -f "./scripts/fix-constraint.sql" ]; then
    echo -e "${RED}SQL fix file not found: ./scripts/fix-constraint.sql${NC}"
    exit 1
fi

# Execute the SQL script in the database container
echo -e "${BLUE}Executing SQL fix...${NC}"
$DOCKER_COMPOSE exec -T db psql -U postgres -d carbon_exchange -f /dev/stdin < ./scripts/fix-constraint.sql

if [ $? -eq 0 ]; then
    echo -e "${GREEN}SQL fix executed successfully!${NC}"
else
    echo -e "${RED}Failed to execute SQL fix.${NC}"
    exit 1
fi

echo -e "${BLUE}Now try running the deployment script again:${NC}"
echo -e "  ./scripts/carbonx-deploy.sh"
