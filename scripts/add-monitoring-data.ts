import { db } from '../src/lib/db';

/**
 * Add sample monitoring data to projects for testing delete functionality
 */
async function addMonitoringData() {
  console.log('Adding sample monitoring data to projects...');

  try {
    // Get the first few projects
    const projects = await db.project.findMany({
      take: 3,
      include: {
        organization: true
      }
    });

    if (projects.length === 0) {
      console.log('No projects found. Please run the comprehensive seed first.');
      return;
    }

    // Get a user to assign as the logger
    const user = await db.user.findFirst();
    if (!user) {
      console.log('No users found. Please run the comprehensive seed first.');
      return;
    }

    console.log(`Found ${projects.length} projects. Adding monitoring data...`);

    for (const project of projects) {
      console.log(`Adding monitoring data to project: ${project.name}`);

      // Create 5-10 monitoring entries for each project
      const entryCount = Math.floor(Math.random() * 6) + 5; // 5-10 entries

      for (let i = 0; i < entryCount; i++) {
        // Create entries for the last 30 days
        const logDate = new Date();
        logDate.setDate(logDate.getDate() - Math.floor(Math.random() * 30));

        const unitTypes = ['SOLAR', 'WIND', 'OUTGOING', 'MWH', 'KWH'];
        const unitType = unitTypes[Math.floor(Math.random() * unitTypes.length)];
        
        // Generate realistic quantities based on unit type
        let quantity;
        if (unitType === 'SOLAR' || unitType === 'WIND') {
          quantity = Math.floor(Math.random() * 1000) + 100; // 100-1100 kWh
        } else if (unitType === 'OUTGOING') {
          quantity = Math.floor(Math.random() * 2000) + 200; // 200-2200 kWh (sum of solar + wind)
        } else {
          quantity = Math.floor(Math.random() * 5000) + 500; // 500-5500 for MWH/KWH
        }

        const frequencies = ['DAILY', 'WEEKLY', 'MONTHLY'];
        const frequency = frequencies[Math.floor(Math.random() * frequencies.length)];

        const dataSources = ['MANUAL', 'CSV_UPLOAD', 'API_INTEGRATION'];
        const dataSource = dataSources[Math.floor(Math.random() * dataSources.length)];

        const verificationStatuses = ['DRAFT', 'SUBMITTED_FOR_VERIFICATION', 'VERIFIED', 'REJECTED'];
        const verificationStatus = verificationStatuses[Math.floor(Math.random() * verificationStatuses.length)];

        await db.unitLog.create({
          data: {
            logDate: logDate,
            frequency: frequency as any,
            unitType: unitType,
            quantity: quantity,
            dataSource: dataSource as any,
            notes: `Sample monitoring data entry ${i + 1} for ${project.name}`,
            metadata: {
              sampleData: true,
              generatedAt: new Date().toISOString(),
              projectType: project.type
            },
            projectId: project.id,
            loggedBy: user.id,
            verificationStatus: verificationStatus as any,
          }
        });

        console.log(`  Created entry ${i + 1}/${entryCount}: ${quantity} ${unitType} on ${logDate.toDateString()}`);
      }

      console.log(`✓ Added ${entryCount} monitoring entries to ${project.name}`);
    }

    console.log('\n✅ Successfully added monitoring data to all projects!');
    console.log('You can now test the delete functionality in the monitoring dashboard.');

  } catch (error) {
    console.error('Error adding monitoring data:', error);
    throw error;
  }
}

// Run the script
addMonitoringData()
  .then(() => {
    console.log('Monitoring data addition completed.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Failed to add monitoring data:', error);
    process.exit(1);
  });
