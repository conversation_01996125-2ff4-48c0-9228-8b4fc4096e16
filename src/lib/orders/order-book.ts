import { OrderSide, OrderStatus, OrderType } from '@prisma/client';
import { db } from '@/lib/db';
import { logger } from '@/lib/logger';
import { OrderBook, OrderBookEntry, MarketSummary } from './types';

/**
 * Order book service
 */
export class OrderBookService {
  /**
   * Get order book for a carbon credit
   * @param carbonCreditId Carbon credit ID
   * @param depth Depth of the order book (number of price levels)
   * @returns Order book
   */
  static async getOrderBook(carbonCreditId: string, depth: number = 10): Promise<OrderBook> {
    try {
      logger.info(`Getting order book for carbon credit ${carbonCreditId} with depth ${depth}`);
      
      // Get buy orders (bids)
      const buyOrders = await db.order.groupBy({
        by: ['price'],
        where: {
          carbonCreditId,
          side: OrderSide.BUY,
          status: OrderStatus.PENDING,
          type: OrderType.LIMIT,
        },
        _sum: {
          quantity: true,
        },
        _count: {
          id: true,
        },
        orderBy: {
          price: 'desc',
        },
        take: depth,
      });
      
      // Get sell orders (asks)
      const sellOrders = await db.order.groupBy({
        by: ['price'],
        where: {
          carbonCreditId,
          side: OrderSide.SELL,
          status: OrderStatus.PENDING,
          type: OrderType.LIMIT,
        },
        _sum: {
          quantity: true,
        },
        _count: {
          id: true,
        },
        orderBy: {
          price: 'asc',
        },
        take: depth,
      });
      
      // Format bids
      const bids: OrderBookEntry[] = buyOrders.map((order) => ({
        price: order.price,
        quantity: order._sum.quantity || 0,
        orderCount: order._count.id,
      }));
      
      // Format asks
      const asks: OrderBookEntry[] = sellOrders.map((order) => ({
        price: order.price,
        quantity: order._sum.quantity || 0,
        orderCount: order._count.id,
      }));
      
      // Calculate spread
      const highestBid = bids.length > 0 ? bids[0].price : 0;
      const lowestAsk = asks.length > 0 ? asks[0].price : 0;
      const spread = lowestAsk > 0 && highestBid > 0 ? lowestAsk - highestBid : 0;
      
      // Get last price from completed transactions
      const lastTransaction = await db.transaction.findFirst({
        where: {
          carbonCreditId,
          type: 'SALE',
          status: 'COMPLETED',
        },
        orderBy: {
          createdAt: 'desc',
        },
        select: {
          amount: true,
          order: {
            select: {
              quantity: true,
            },
          },
        },
      });
      
      // Calculate last price
      const lastPrice = lastTransaction && lastTransaction.order
        ? lastTransaction.amount / lastTransaction.order.quantity
        : undefined;
      
      return {
        carbonCreditId,
        bids,
        asks,
        spread,
        lastPrice,
        lastUpdated: new Date(),
      };
    } catch (error) {
      logger.error(`Error getting order book for carbon credit ${carbonCreditId}:`, error);
      throw new Error(`Failed to get order book: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get market summary for a carbon credit
   * @param carbonCreditId Carbon credit ID
   * @param period Period in hours (default: 24)
   * @returns Market summary
   */
  static async getMarketSummary(carbonCreditId: string, period: number = 24): Promise<MarketSummary> {
    try {
      logger.info(`Getting market summary for carbon credit ${carbonCreditId} with period ${period} hours`);
      
      // Calculate start date
      const startDate = new Date();
      startDate.setHours(startDate.getHours() - period);
      
      // Get transactions in the period
      const transactions = await db.transaction.findMany({
        where: {
          carbonCreditId,
          type: 'TRADE',
          status: 'COMPLETED',
          createdAt: {
            gte: startDate,
          },
        },
        include: {
          order: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
      
      // Get transactions in the previous period
      const previousStartDate = new Date(startDate);
      previousStartDate.setHours(previousStartDate.getHours() - period);
      
      const previousTransactions = await db.transaction.findMany({
        where: {
          carbonCreditId,
          type: 'TRADE',
          status: 'COMPLETED',
          createdAt: {
            gte: previousStartDate,
            lt: startDate,
          },
        },
        include: {
          order: true,
        },
      });
      
      // Calculate last price
      const lastTransaction = transactions.length > 0 ? transactions[0] : null;
      const lastPrice = lastTransaction && lastTransaction.order
        ? lastTransaction.amount / lastTransaction.order.quantity
        : undefined;
      
      // Calculate high and low prices
      let highPrice: number | undefined;
      let lowPrice: number | undefined;
      
      for (const transaction of transactions) {
        if (transaction.order) {
          const price = transaction.amount / transaction.order.quantity;
          
          if (highPrice === undefined || price > highPrice) {
            highPrice = price;
          }
          
          if (lowPrice === undefined || price < lowPrice) {
            lowPrice = price;
          }
        }
      }
      
      // Calculate volume
      const volume = transactions.reduce((sum, transaction) => sum + transaction.amount, 0);
      
      // Calculate previous volume
      const previousVolume = previousTransactions.reduce((sum, transaction) => sum + transaction.amount, 0);
      
      // Calculate volume change
      const volumeChange = previousVolume > 0 ? (volume - previousVolume) / previousVolume * 100 : 0;
      
      // Calculate percent change
      let percentChange = 0;
      
      if (transactions.length > 0 && previousTransactions.length > 0) {
        const firstTransaction = transactions[transactions.length - 1];
        const firstPrice = firstTransaction.order
          ? firstTransaction.amount / firstTransaction.order.quantity
          : 0;
        
        if (firstPrice > 0 && lastPrice !== undefined) {
          percentChange = (lastPrice - firstPrice) / firstPrice * 100;
        }
      }
      
      // Get order count
      const orderCount = await db.order.count({
        where: {
          carbonCreditId,
          status: OrderStatus.PENDING,
        },
      });
      
      return {
        carbonCreditId,
        lastPrice,
        highPrice,
        lowPrice,
        volume,
        volumeChange,
        percentChange,
        orderCount,
        lastUpdated: new Date(),
      };
    } catch (error) {
      logger.error(`Error getting market summary for carbon credit ${carbonCreditId}:`, error);
      throw new Error(`Failed to get market summary: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get market summaries for all carbon credits
   * @param period Period in hours (default: 24)
   * @returns Market summaries
   */
  static async getAllMarketSummaries(period: number = 24): Promise<MarketSummary[]> {
    try {
      logger.info(`Getting market summaries for all carbon credits with period ${period} hours`);
      
      // Get all carbon credits with transactions
      const carbonCredits = await db.carbonCredit.findMany({
        where: {
          transactions: {
            some: {
              type: 'TRADE',
              status: 'COMPLETED',
            },
          },
        },
        select: {
          id: true,
        },
      });
      
      // Get market summary for each carbon credit
      const summaries = await Promise.all(
        carbonCredits.map((carbonCredit) => this.getMarketSummary(carbonCredit.id, period))
      );
      
      return summaries;
    } catch (error) {
      logger.error(`Error getting market summaries:`, error);
      throw new Error(`Failed to get market summaries: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get price history for a carbon credit
   * @param carbonCreditId Carbon credit ID
   * @param period Period in days (default: 30)
   * @param interval Interval in hours (default: 24)
   * @returns Price history
   */
  static async getPriceHistory(
    carbonCreditId: string,
    period: number = 30,
    interval: number = 24
  ): Promise<{ timestamp: Date; price: number; volume: number }[]> {
    try {
      logger.info(`Getting price history for carbon credit ${carbonCreditId} with period ${period} days and interval ${interval} hours`);
      
      // Calculate start date
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - period);
      
      // Get transactions in the period
      const transactions = await db.transaction.findMany({
        where: {
          carbonCreditId,
          type: 'TRADE',
          status: 'COMPLETED',
          createdAt: {
            gte: startDate,
          },
        },
        include: {
          order: true,
        },
        orderBy: {
          createdAt: 'asc',
        },
      });
      
      // Group transactions by interval
      const intervals: { timestamp: Date; price: number; volume: number }[] = [];
      
      // Create intervals
      for (let i = 0; i < period * 24 / interval; i++) {
        const intervalStart = new Date(startDate);
        intervalStart.setHours(intervalStart.getHours() + i * interval);
        
        const intervalEnd = new Date(intervalStart);
        intervalEnd.setHours(intervalEnd.getHours() + interval);
        
        // Filter transactions in this interval
        const intervalTransactions = transactions.filter(
          (tx) => tx.createdAt >= intervalStart && tx.createdAt < intervalEnd
        );
        
        // Calculate volume
        const volume = intervalTransactions.reduce((sum, tx) => sum + tx.amount, 0);
        
        // Calculate average price
        let price = 0;
        let totalQuantity = 0;
        
        for (const tx of intervalTransactions) {
          if (tx.order) {
            price += tx.amount;
            totalQuantity += tx.order.quantity;
          }
        }
        
        price = totalQuantity > 0 ? price / totalQuantity : 0;
        
        // Add interval
        intervals.push({
          timestamp: intervalStart,
          price,
          volume,
        });
      }
      
      // Fill in missing prices
      let lastPrice = 0;
      
      for (let i = 0; i < intervals.length; i++) {
        if (intervals[i].price === 0) {
          intervals[i].price = lastPrice;
        } else {
          lastPrice = intervals[i].price;
        }
      }
      
      return intervals;
    } catch (error) {
      logger.error(`Error getting price history for carbon credit ${carbonCreditId}:`, error);
      throw new Error(`Failed to get price history: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
}
