import { render, screen } from '@testing-library/react';
import { SPVDetailsView } from '@/components/spv/spv-details-view';
import { SPVWithDetails } from '@/types/spv';

// Mock the spvUtils
jest.mock('@/lib/api/spv', () => ({
  spvUtils: {
    formatDate: (date: string) => new Date(date).toLocaleDateString(),
    formatDateTime: (date: string) => new Date(date).toLocaleString(),
    formatStatus: (status: string) => status.charAt(0) + status.slice(1).toLowerCase(),
  },
}));

const mockSPV: SPVWithDetails = {
  id: 'spv-1',
  name: 'Test SPV',
  purpose: 'Test purpose',
  jurisdiction: 'Test jurisdiction',
  status: 'ACTIVE',
  establishedDate: '2023-01-01',
  legalStructure: 'LLC',
  registrationNumber: 'REG123',
  taxId: 'TAX123',
  address: '123 Test St',
  description: 'Test description',
  country: 'US',
  legalEntityId: 'LE123',
  contact: '<EMAIL>',
  projectCategories: ['renewable'],
  metadata: null,
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z',
  organizationId: 'org-1',
  organization: {
    id: 'org-1',
    name: 'Test Organization',
    legalName: 'Test Organization LLC',
    status: 'ACTIVE',
    verificationStatus: 'VERIFIED',
    country: 'US',
    industry: 'Technology',
    email: '<EMAIL>',
    phoneNumber: '+1234567890',
  },
  projects: [
    {
      id: 'project-1',
      name: 'Test Project',
      status: 'ACTIVE',
      type: 'Solar',
      createdAt: '2023-01-01T00:00:00Z',
      estimatedReductions: 1000,
    },
  ],
  spvUsers: [
    {
      id: 'spv-user-1',
      role: 'SPV_ADMIN',
      isActive: true,
      createdAt: '2023-01-01T00:00:00Z',
      user: {
        id: 'user-1',
        name: 'Test User',
        email: '<EMAIL>',
      },
    },
  ],
  _count: {
    projects: 1,
    spvUsers: 1,
  },
};

describe('SPVDetailsView', () => {
  const defaultProps = {
    spv: mockSPV,
    isOpen: true,
    onClose: jest.fn(),
    showActions: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders SPV basic information', () => {
    render(<SPVDetailsView {...defaultProps} />);
    
    expect(screen.getByText('Test SPV')).toBeInTheDocument();
    expect(screen.getByText('LLC • Test jurisdiction')).toBeInTheDocument();
  });

  it('renders organization information', () => {
    render(<SPVDetailsView {...defaultProps} />);
    
    expect(screen.getByText('Organization')).toBeInTheDocument();
    expect(screen.getByText('Test Organization')).toBeInTheDocument();
  });

  it('renders SPV users section', () => {
    render(<SPVDetailsView {...defaultProps} />);
    
    expect(screen.getByText('SPV Users (1)')).toBeInTheDocument();
    expect(screen.getByText('Test User')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('Role: SPV ADMIN')).toBeInTheDocument();
  });

  it('renders projects section', () => {
    render(<SPVDetailsView {...defaultProps} />);
    
    expect(screen.getByText('Associated Projects (1)')).toBeInTheDocument();
    expect(screen.getByText('Test Project')).toBeInTheDocument();
    expect(screen.getByText('Status: ACTIVE')).toBeInTheDocument();
    expect(screen.getByText('Type: Solar')).toBeInTheDocument();
  });

  it('shows empty state when no users', () => {
    const spvWithoutUsers = {
      ...mockSPV,
      spvUsers: [],
      _count: { ...mockSPV._count, spvUsers: 0 },
    };

    render(<SPVDetailsView {...defaultProps} spv={spvWithoutUsers} />);
    
    expect(screen.getByText('No users assigned to this SPV')).toBeInTheDocument();
  });

  it('shows empty state when no projects', () => {
    const spvWithoutProjects = {
      ...mockSPV,
      projects: [],
      _count: { ...mockSPV._count, projects: 0 },
    };

    render(<SPVDetailsView {...defaultProps} spv={spvWithoutProjects} />);
    
    expect(screen.getByText('No projects associated with this SPV')).toBeInTheDocument();
  });
});
