import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { NextRequest } from 'next/server';
import { POST as createUser } from '@/app/api/spv/users/route';
import { POST as assignProject } from '@/app/api/spv/project-assignments/route';
import { GET as getUsers } from '@/app/api/spv/users/route';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { hashPassword } from '@/lib/auth/password';

// Mock dependencies
vi.mock('@/lib/auth');
vi.mock('@/lib/db');
vi.mock('@/lib/auth/password');
vi.mock('@/lib/logger', () => ({
  logger: {
    info: vi.fn(),
    error: vi.fn(),
  },
}));

const mockAuth = vi.mocked(auth);
const mockDb = vi.mocked(db);
const mockHashPassword = vi.mocked(hashPassword);

describe('SPV User Management Integration', () => {
  const mockSpvAdmin = {
    user: { id: 'admin1', role: 'SPV_USER' },
  };

  const mockSpvUser = {
    id: 'spvadmin1',
    role: 'SPV_ADMIN',
    spvId: 'spv1',
    spv: {
      id: 'spv1',
      name: 'Test SPV',
      organizationId: 'org1',
      organization: {
        id: 'org1',
        name: 'Test Organization',
      },
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup default auth mock
    mockAuth.mockResolvedValue(mockSpvAdmin as any);
    
    // Setup default SPV user mock
    mockDb.sPVUser.findFirst.mockResolvedValue(mockSpvUser as any);
    
    mockHashPassword.mockResolvedValue('hashedpassword');
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('Complete User Creation and Project Assignment Workflow', () => {
    it('should create user and assign projects in one flow', async () => {
      // Mock user creation
      const mockCreatedUser = {
        id: 'newuser1',
        email: '<EMAIL>',
        name: 'Project Manager',
        role: 'SPV_USER',
        organizationId: 'org1',
      };

      const mockCreatedSpvUser = {
        id: 'newspvuser1',
        userId: 'newuser1',
        spvId: 'spv1',
        role: 'PROJECT_MANAGER',
        user: mockCreatedUser,
        spv: mockSpvUser.spv,
      };

      // Mock no existing user
      mockDb.user.findUnique.mockResolvedValue(null);

      // Mock transaction for user creation with project assignment
      mockDb.$transaction.mockImplementation(async (callback) => {
        const tx = {
          user: {
            create: vi.fn().mockResolvedValue(mockCreatedUser),
          },
          sPVUser: {
            create: vi.fn().mockResolvedValue(mockCreatedSpvUser),
          },
          project: {
            findMany: vi.fn().mockResolvedValue([
              { id: 'project1', name: 'Project 1', spvId: 'spv1' },
              { id: 'project2', name: 'Project 2', spvId: 'spv1' },
            ]),
          },
          projectAssignment: {
            createMany: vi.fn().mockResolvedValue({ count: 2 }),
          },
        };
        return await callback(tx as any);
      });

      // Create user with project assignments
      const createUserRequest = new NextRequest('http://localhost:3000/api/spv/users', {
        method: 'POST',
        body: JSON.stringify({
          email: '<EMAIL>',
          name: 'Project Manager',
          role: 'PROJECT_MANAGER',
          jobTitle: 'Senior PM',
          phoneNumber: '+1234567890',
          password: 'password123',
          projectIds: ['project1', 'project2'],
        }),
      });

      const createResponse = await createUser(createUserRequest);
      expect(createResponse.status).toBe(200);

      const createData = await createResponse.json();
      expect(createData.success).toBe(true);
      expect(createData.data.id).toBe('newspvuser1');

      // Verify user creation was called correctly
      expect(mockDb.$transaction).toHaveBeenCalled();
    });

    it('should handle permission validation correctly', async () => {
      // Test with non-admin user
      mockDb.sPVUser.findFirst.mockResolvedValue({
        id: 'spvuser1',
        role: 'SITE_WORKER', // Not admin
        spvId: 'spv1',
      } as any);

      const request = new NextRequest('http://localhost:3000/api/spv/users', {
        method: 'POST',
        body: JSON.stringify({
          email: '<EMAIL>',
          name: 'Test User',
          role: 'PROJECT_MANAGER',
          password: 'password123',
        }),
      });

      const response = await createUser(request);
      expect(response.status).toBe(403);

      const data = await response.json();
      expect(data.error).toBe('Access denied. SPV admin role required.');
    });

    it('should validate organization boundaries', async () => {
      // Mock user from different organization
      const differentOrgSpvUser = {
        id: 'spvadmin2',
        role: 'SPV_ADMIN',
        spvId: 'spv2',
        spv: {
          id: 'spv2',
          organizationId: 'org2', // Different organization
        },
      };

      mockDb.sPVUser.findFirst.mockResolvedValue(differentOrgSpvUser as any);

      // Mock existing user in different org
      mockDb.user.findUnique.mockResolvedValue(null);

      const mockCreatedUser = {
        id: 'newuser2',
        email: '<EMAIL>',
        organizationId: 'org2', // Should be assigned to correct org
      };

      const mockCreatedSpvUser = {
        id: 'newspvuser2',
        userId: 'newuser2',
        spvId: 'spv2',
        user: mockCreatedUser,
        spv: differentOrgSpvUser.spv,
      };

      mockDb.$transaction.mockImplementation(async (callback) => {
        const tx = {
          user: {
            create: vi.fn().mockResolvedValue(mockCreatedUser),
          },
          sPVUser: {
            create: vi.fn().mockResolvedValue(mockCreatedSpvUser),
          },
        };
        return await callback(tx as any);
      });

      const request = new NextRequest('http://localhost:3000/api/spv/users', {
        method: 'POST',
        body: JSON.stringify({
          email: '<EMAIL>',
          name: 'Test User',
          role: 'PROJECT_MANAGER',
          password: 'password123',
        }),
      });

      const response = await createUser(request);
      expect(response.status).toBe(200);

      // Verify user was created with correct organization
      expect(mockDb.$transaction).toHaveBeenCalled();
    });

    it('should handle separate project assignment after user creation', async () => {
      // First, create a user without projects
      const mockCreatedSpvUser = {
        id: 'newspvuser1',
        userId: 'newuser1',
        spvId: 'spv1',
        role: 'PROJECT_MANAGER',
      };

      mockDb.user.findUnique.mockResolvedValue(null);
      mockDb.$transaction.mockImplementation(async (callback) => {
        const tx = {
          user: {
            create: vi.fn().mockResolvedValue({ id: 'newuser1' }),
          },
          sPVUser: {
            create: vi.fn().mockResolvedValue(mockCreatedSpvUser),
          },
        };
        return await callback(tx as any);
      });

      // Create user without projects
      const createUserRequest = new NextRequest('http://localhost:3000/api/spv/users', {
        method: 'POST',
        body: JSON.stringify({
          email: '<EMAIL>',
          name: 'Project Manager',
          role: 'PROJECT_MANAGER',
          password: 'password123',
        }),
      });

      const createResponse = await createUser(createUserRequest);
      expect(createResponse.status).toBe(200);

      // Reset mocks for project assignment
      vi.clearAllMocks();
      mockAuth.mockResolvedValue(mockSpvAdmin as any);
      mockDb.sPVUser.findFirst.mockResolvedValue(mockSpvUser as any);

      // Mock target SPV user for assignment
      mockDb.sPVUser.findFirst.mockResolvedValueOnce({
        id: 'newspvuser1',
        spvId: 'spv1',
        user: {
          id: 'newuser1',
          name: 'Project Manager',
          email: '<EMAIL>',
        },
      } as any);

      // Mock projects
      mockDb.project.findMany.mockResolvedValue([
        { id: 'project1', name: 'Project 1', spvId: 'spv1' },
      ] as any);

      // Mock no existing assignments
      mockDb.projectAssignment.findMany.mockResolvedValue([]);
      mockDb.projectAssignment.createMany.mockResolvedValue({ count: 1 });

      // Now assign projects
      const assignProjectRequest = new NextRequest('http://localhost:3000/api/spv/project-assignments', {
        method: 'POST',
        body: JSON.stringify({
          spvUserId: 'newspvuser1',
          projectIds: ['project1'],
        }),
      });

      const assignResponse = await assignProject(assignProjectRequest);
      expect(assignResponse.status).toBe(200);

      const assignData = await assignResponse.json();
      expect(assignData.success).toBe(true);
      expect(assignData.data.assignmentsCreated).toBe(1);
    });

    it('should maintain data consistency across operations', async () => {
      // Test that users created by SPV admins are visible in the users list
      
      // Mock users list including newly created user
      const mockUsers = [
        {
          id: 'spvuser1',
          role: 'PROJECT_MANAGER',
          isActive: true,
          createdAt: new Date(),
          user: {
            id: 'user1',
            email: '<EMAIL>',
            name: 'Project Manager',
            jobTitle: 'Senior PM',
          },
          spv: {
            id: 'spv1',
            name: 'Test SPV',
            status: 'ACTIVE',
            organization: {
              id: 'org1',
              name: 'Test Org',
            },
          },
          projectAssignments: [
            {
              id: 'assignment1',
              project: {
                id: 'project1',
                name: 'Test Project',
              },
            },
          ],
        },
      ];

      mockDb.sPVUser.findMany.mockResolvedValue(mockUsers as any);
      mockDb.sPVUser.count.mockResolvedValue(1);

      const getUsersRequest = new NextRequest('http://localhost:3000/api/spv/users');
      const getUsersResponse = await getUsers(getUsersRequest);

      expect(getUsersResponse.status).toBe(200);
      const getUsersData = await getUsersResponse.json();
      expect(getUsersData.users).toHaveLength(1);
      expect(getUsersData.users[0].user.email).toBe('<EMAIL>');
      expect(getUsersData.users[0].projectAssignments).toHaveLength(1);
    });
  });
});
