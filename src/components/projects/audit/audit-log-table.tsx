"use client";

import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  FileText,
  User,
  Calendar,
  Filter,
  Download,
  MoreHorizontal,
  Eye,
  Database,
  Upload,
  Edit,
  CheckCircle,
  XCircle,
  Clock,
  Activity,
  Settings,
  Zap,
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";

export interface AuditLog {
  id: string;
  type: string;
  description: string;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  createdAt: string;
  user?: {
    id: string;
    name: string;
    email: string;
  } | null;
}

interface AuditLogTableProps {
  auditLogs: AuditLog[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasMore: boolean;
  };
  summary: Record<string, number>;
  onPageChange: (page: number) => void;
  onFilterChange: (filters: AuditLogFilters) => void;
  onExport: () => void;
  isLoading?: boolean;
}

export interface AuditLogFilters {
  type?: string;
  userId?: string;
  startDate?: string;
  endDate?: string;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

const AUDIT_LOG_TYPES = [
  { value: "all", label: "All Types" },
  { value: "PROJECT_", label: "Project Activities" },
  { value: "DATA_ENTRY_", label: "Data Entry" },
  { value: "DATA_", label: "Data Management" },
  { value: "MONITORING_", label: "Monitoring" },
  { value: "API_", label: "API Integration" },
];

export function AuditLogTable({
  auditLogs,
  pagination,
  summary,
  onPageChange,
  onFilterChange,
  onExport,
  isLoading = false,
}: AuditLogTableProps) {
  const [filters, setFilters] = useState<AuditLogFilters>({
    type: "all",
    sortBy: "createdAt",
    sortOrder: "desc",
  });

  const handleFilterChange = (newFilters: Partial<AuditLogFilters>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    onFilterChange(updatedFilters);
  };

  const getAuditLogIcon = (type: string) => {
    if (type.startsWith("PROJECT_")) return <FileText className="h-4 w-4" />;
    if (type.startsWith("DATA_ENTRY_")) return <Upload className="h-4 w-4" />;
    if (type.startsWith("DATA_")) return <Database className="h-4 w-4" />;
    if (type.startsWith("MONITORING_")) return <Activity className="h-4 w-4" />;
    if (type.startsWith("API_")) return <Zap className="h-4 w-4" />;
    return <Settings className="h-4 w-4" />;
  };

  const getAuditLogBadgeVariant = (type: string) => {
    if (type.includes("CREATED") || type.includes("APPROVED")) return "default";
    if (type.includes("UPDATED") || type.includes("VERIFIED")) return "secondary";
    if (type.includes("DELETED") || type.includes("REJECTED")) return "destructive";
    if (type.includes("PENDING") || type.includes("REQUESTED")) return "outline";
    return "secondary";
  };

  const formatAuditLogType = (type: string) => {
    return type
      .split("_")
      .map(word => word.charAt(0) + word.slice(1).toLowerCase())
      .join(" ");
  };

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
            <div>
              <Select
                value={filters.type}
                onValueChange={(value) => handleFilterChange({ type: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  {AUDIT_LOG_TYPES.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Input
                type="date"
                placeholder="Start date"
                onChange={(e) => handleFilterChange({ startDate: e.target.value })}
              />
            </div>
            <div className="flex gap-2">
              <Input
                type="date"
                placeholder="End date"
                onChange={(e) => handleFilterChange({ endDate: e.target.value })}
              />
              <Button variant="outline" onClick={onExport}>
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Summary Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">Total Events</p>
                <p className="text-2xl font-bold">{pagination.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Upload className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">Data Entries</p>
                <p className="text-2xl font-bold">
                  {Object.entries(summary)
                    .filter(([key]) => key.startsWith("DATA_ENTRY_"))
                    .reduce((sum, [, count]) => sum + count, 0)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">Approvals</p>
                <p className="text-2xl font-bold">
                  {Object.entries(summary)
                    .filter(([key]) => key.includes("APPROVED"))
                    .reduce((sum, [, count]) => sum + count, 0)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Activity className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">API Events</p>
                <p className="text-2xl font-bold">
                  {Object.entries(summary)
                    .filter(([key]) => key.startsWith("API_"))
                    .reduce((sum, [, count]) => sum + count, 0)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Audit Log Table */}
      <Card>
        <CardHeader>
          <CardTitle>Audit Events</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Event</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>User</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead className="w-[50px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8">
                      <div className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                        <span className="ml-2">Loading audit events...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : auditLogs.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8">
                      <div className="flex flex-col items-center gap-2">
                        <FileText className="h-8 w-8 text-muted-foreground" />
                        <p className="text-muted-foreground">No audit events found</p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  auditLogs.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getAuditLogIcon(log.type)}
                          <Badge variant={getAuditLogBadgeVariant(log.type)}>
                            {formatAuditLogType(log.type)}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="max-w-md">
                          <p className="text-sm">{log.description}</p>
                          {log.metadata && (
                            <p className="text-xs text-muted-foreground mt-1">
                              {log.metadata.unitLogId && `Entry ID: ${log.metadata.unitLogId}`}
                              {log.metadata.totalEntries && ` • ${log.metadata.totalEntries} entries`}
                            </p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="text-sm font-medium">
                              {log.user?.name || "System"}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {log.user?.email || "<EMAIL>"}
                            </p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="text-sm">
                              {formatDistanceToNow(new Date(log.createdAt), { addSuffix: true })}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {new Date(log.createdAt).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex items-center justify-between mt-4">
              <p className="text-sm text-muted-foreground">
                Showing {((pagination.page - 1) * pagination.limit) + 1} to{" "}
                {Math.min(pagination.page * pagination.limit, pagination.total)} of{" "}
                {pagination.total} entries
              </p>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onPageChange(pagination.page - 1)}
                  disabled={pagination.page === 1}
                >
                  Previous
                </Button>
                <span className="text-sm">
                  Page {pagination.page} of {pagination.totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onPageChange(pagination.page + 1)}
                  disabled={!pagination.hasMore}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
