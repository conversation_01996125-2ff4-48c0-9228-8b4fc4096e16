"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { format } from "date-fns";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { toast } from "@/components/ui/use-toast";
import { CheckCircle, XCircle, Clock, Loader2, FileCheck, AlertTriangle } from "lucide-react";

// Schema for verification workflow
const verificationSchema = z.object({
  action: z.enum(["SUBMIT_FOR_VERIFICATION", "VERIFY", "REJECT"]),
  notes: z.string().max(1000, "Notes must be less than 1000 characters").optional(),
});

type VerificationFormValues = z.infer<typeof verificationSchema>;

interface UnitLog {
  id: string;
  logDate: string;
  unitType: string;
  quantity: number;
  verificationStatus: string;
  dataSource: string;
  metadata?: any;
  logger?: {
    name: string;
    email: string;
  };
  verifier?: {
    name: string;
    email: string;
  };
}

interface VerificationWorkflowModalProps {
  isOpen: boolean;
  onClose: () => void;
  unitLog: UnitLog | null;
  projectId: string;
  onSuccess: () => void;
  userRole?: string; // To determine available actions
}

export function VerificationWorkflowModal({
  isOpen,
  onClose,
  unitLog,
  projectId,
  onSuccess,
  userRole = "USER",
}: VerificationWorkflowModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<VerificationFormValues>({
    resolver: zodResolver(verificationSchema),
    defaultValues: {
      action: "SUBMIT_FOR_VERIFICATION",
      notes: "",
    },
  });

  const canVerify = userRole === "ADMIN" || userRole === "VERIFIER";
  const canSubmitForVerification = unitLog?.verificationStatus === "PENDING" && !canVerify;

  const onSubmit = async (data: VerificationFormValues) => {
    if (!unitLog) return;

    try {
      setIsSubmitting(true);

      let endpoint = "";
      let method = "";
      let body: any = {};

      if (data.action === "SUBMIT_FOR_VERIFICATION") {
        // For now, we'll just update the status to indicate it's ready for verification
        endpoint = `/api/projects/${projectId}/unit-logs/${unitLog.id}`;
        method = "PATCH";
        body = {
          verificationStatus: "PENDING",
          verificationNotes: data.notes,
        };
      } else {
        // Verify or Reject
        endpoint = `/api/projects/${projectId}/unit-logs/${unitLog.id}`;
        method = "PATCH";
        body = {
          verificationStatus: data.action === "VERIFY" ? "VERIFIED" : "REJECTED",
          verificationNotes: data.notes,
        };
      }

      const response = await fetch(endpoint, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || errorData.message || "Failed to update verification status");
      }

      const actionText = data.action === "VERIFY" ? "verified" : 
                        data.action === "REJECT" ? "rejected" : 
                        "submitted for verification";

      toast({
        title: "Success",
        description: `Entry ${actionText} successfully`,
      });

      onSuccess();
      onClose();
    } catch (error) {
      console.error("Error updating verification status:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update verification status",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
      form.reset();
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "VERIFIED":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "REJECTED":
        return <XCircle className="h-4 w-4 text-red-600" />;
      case "PENDING":
        return <Clock className="h-4 w-4 text-yellow-600" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      VERIFIED: "bg-green-100 text-green-800",
      REJECTED: "bg-red-100 text-red-800",
      PENDING: "bg-yellow-100 text-yellow-800",
    };
    
    return (
      <Badge className={variants[status as keyof typeof variants] || "bg-gray-100 text-gray-800"}>
        {getStatusIcon(status)}
        <span className="ml-1">{status}</span>
      </Badge>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileCheck className="h-5 w-5 text-blue-600" />
            Verification Workflow
          </DialogTitle>
          <DialogDescription>
            Manage the verification status of this monitoring data entry.
          </DialogDescription>
        </DialogHeader>

        {unitLog && (
          <div className="space-y-4">
            {/* Entry Details */}
            <div className="p-4 bg-gray-50 rounded-lg border">
              <h4 className="font-medium text-gray-900 mb-3">Entry Details</h4>
              <div className="grid grid-cols-2 gap-3 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Date:</span>
                  <span className="ml-2 text-gray-900">
                    {format(new Date(unitLog.logDate), "PPP")}
                  </span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Unit Type:</span>
                  <span className="ml-2 text-gray-900">{unitLog.unitType}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Quantity:</span>
                  <span className="ml-2 text-gray-900">{unitLog.quantity.toLocaleString()}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Source:</span>
                  <span className="ml-2 text-gray-900">{unitLog.dataSource}</span>
                </div>
                <div className="col-span-2">
                  <span className="font-medium text-gray-700">Current Status:</span>
                  <span className="ml-2">{getStatusBadge(unitLog.verificationStatus)}</span>
                </div>
                {unitLog.logger && (
                  <div className="col-span-2">
                    <span className="font-medium text-gray-700">Logged by:</span>
                    <span className="ml-2 text-gray-900">{unitLog.logger.name}</span>
                  </div>
                )}
                {unitLog.verifier && (
                  <div className="col-span-2">
                    <span className="font-medium text-gray-700">Verified by:</span>
                    <span className="ml-2 text-gray-900">{unitLog.verifier.name}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Verification Actions */}
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                {/* Action Selection */}
                <div className="space-y-3">
                  <h4 className="font-medium text-gray-900">Available Actions</h4>
                  
                  {canVerify && unitLog.verificationStatus === "PENDING" && (
                    <div className="flex gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        className="flex-1 border-green-200 hover:bg-green-50"
                        onClick={() => form.setValue("action", "VERIFY")}
                        disabled={isSubmitting}
                      >
                        <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                        Verify Entry
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        className="flex-1 border-red-200 hover:bg-red-50"
                        onClick={() => form.setValue("action", "REJECT")}
                        disabled={isSubmitting}
                      >
                        <XCircle className="mr-2 h-4 w-4 text-red-600" />
                        Reject Entry
                      </Button>
                    </div>
                  )}

                  {canSubmitForVerification && (
                    <Button
                      type="button"
                      variant="outline"
                      className="w-full border-blue-200 hover:bg-blue-50"
                      onClick={() => form.setValue("action", "SUBMIT_FOR_VERIFICATION")}
                      disabled={isSubmitting}
                    >
                      <FileCheck className="mr-2 h-4 w-4 text-blue-600" />
                      Submit for Verification
                    </Button>
                  )}

                  {!canVerify && !canSubmitForVerification && (
                    <div className="text-center py-4 text-gray-500">
                      No actions available for this entry.
                    </div>
                  )}
                </div>

                {/* Notes */}
                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {form.watch("action") === "VERIFY" ? "Verification Notes" :
                         form.watch("action") === "REJECT" ? "Rejection Reason" :
                         "Submission Notes"}
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder={
                            form.watch("action") === "VERIFY" ? "Add any verification notes or observations..." :
                            form.watch("action") === "REJECT" ? "Please explain why this entry is being rejected..." :
                            "Add any notes for the verifier..."
                          }
                          className="resize-none h-24"
                          maxLength={1000}
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        {form.watch("action") === "REJECT" ? "Required: Please provide a reason for rejection" :
                         "Optional: Add any relevant notes or comments"}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleClose}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    {form.watch("action") === "VERIFY" ? "Verify Entry" :
                     form.watch("action") === "REJECT" ? "Reject Entry" :
                     "Submit for Verification"}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
