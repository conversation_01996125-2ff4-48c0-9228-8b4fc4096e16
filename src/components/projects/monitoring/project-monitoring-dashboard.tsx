"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { toast } from "@/components/ui/use-toast";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  Area,
  AreaChart
} from "recharts";
import {
  Zap,
  TrendingUp,
  TrendingDown,
  Calendar,
  FileText,
  Upload,
  Settings,
  AlertCircle,
  CheckCircle,
  Clock,
  Database,
  Wifi,
  FileSpreadsheet,
  Plus,
  Eye,
  Edit,
  Download,
  RefreshCw
} from "lucide-react";
import { PowerGenerationSummary } from "../power-generation-summary";
import { PowerGenerationChart } from "../power-generation-chart";
import { EstimatedCarbonCredits } from "../estimated-carbon-credits";
import { MonitoringDataTable } from "./monitoring-data-table";

interface ProjectMonitoringDashboardProps {
  projectId: string;
  project: {
    id: string;
    name: string;
    type: string;
    status: string;
    startDate: string;
    estimatedReductions: number;
    metadata?: {
      projectSubType?: string;
      [key: string]: any;
    };
  };
}

interface UnitLog {
  id: string;
  logDate: string;
  frequency: string;
  unitType: string;
  quantity: number;
  dataSource: string;
  verificationStatus: string;
  loggedBy: string;

}

interface EmissionCalculation {
  id: string;
  calculationPeriod: string;
  powerGenerated: number;
  emissionReduction: number;
  baselineEmissions: number;
  projectEmissions: number;
  status: string;
}

interface BaselineConfiguration {
  id: string;
  baselineType: string;
  gridEmissionFactor: number;
  baselineYear: number;
  status: string;
}

export function ProjectMonitoringDashboard({ projectId, project }: ProjectMonitoringDashboardProps) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("overview");
  const [isLoading, setIsLoading] = useState(true);
  const [unitLogs, setUnitLogs] = useState<UnitLog[]>([]);
  const [emissionCalculations, setEmissionCalculations] = useState<EmissionCalculation[]>([]);
  const [baselineConfig, setBaselineConfig] = useState<BaselineConfiguration | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Mock data for demonstration
  useEffect(() => {
    loadMonitoringData();
  }, [projectId]);

  const loadMonitoringData = async () => {
    try {
      setIsLoading(true);

      // Fetch real unit logs data from API (all entries for accurate totals)
      const response = await fetch(`/api/projects/${projectId}/unit-logs?limit=10000`);

      if (!response.ok) {
        throw new Error("Failed to fetch monitoring data");
      }

      const data = await response.json();
      const realUnitLogs = data.unitLogs || [];

      // Transform API data to match UnitLog interface
      const transformedUnitLogs: UnitLog[] = realUnitLogs.map((log: any) => ({
        id: log.id,
        logDate: log.logDate,
        frequency: log.frequency || "DAILY",
        unitType: log.unitType,
        quantity: Number(log.quantity) || 0,
        dataSource: log.dataSource,
        verificationStatus: log.verificationStatus,
        loggedBy: log.logger?.name || "Unknown"
      }));

      // Calculate emission reductions based on real data
      const totalPowerGenerated = transformedUnitLogs.reduce((sum, log) => sum + log.quantity, 0);
      const emissionFactor = 0.5; // Default grid emission factor (tCO2e per MWh)
      const totalEmissionReduction = (totalPowerGenerated / 1000) * emissionFactor; // Convert kWh to MWh

      const calculatedEmissionCalculations: EmissionCalculation[] = [
        {
          id: "calculated-1",
          calculationPeriod: "Current Period",
          powerGenerated: totalPowerGenerated,
          emissionReduction: totalEmissionReduction,
          baselineEmissions: totalEmissionReduction,
          projectEmissions: 0,
          status: "CALCULATED"
        }
      ];

      const defaultBaselineConfig: BaselineConfiguration = {
        id: "default-1",
        baselineType: "GRID_EMISSION_FACTOR",
        gridEmissionFactor: emissionFactor,
        baselineYear: new Date().getFullYear() - 1,
        status: "APPROVED"
      };

      setUnitLogs(transformedUnitLogs);
      setEmissionCalculations(calculatedEmissionCalculations);
      setBaselineConfig(defaultBaselineConfig);
    } catch (error) {
      console.error("Error loading monitoring data:", error);
      toast({
        title: "Error",
        description: "Failed to load monitoring data",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const refreshData = async () => {
    setRefreshing(true);
    await loadMonitoringData();
    setRefreshing(false);
    toast({
      title: "Data Refreshed",
      description: "Monitoring data has been updated"
    });
  };

  const handleDataEntrySuccess = () => {
    // Trigger refresh of all data components
    setRefreshTrigger(prev => prev + 1);
    loadMonitoringData();
  };

  // Calculate number of rows (not individual entries)
  // For hybrid projects, group by date since each CSV row creates 3 entries with same date
  // For standard projects, each entry represents one row
  const calculateRowCount = (logs: any[]) => {
    const isHybridProject = project.type === "RENEWABLE_ENERGY" && project.metadata?.projectSubType === "HYBRID_RENEWABLE";

    if (isHybridProject) {
      // Group by date to count unique CSV rows
      const uniqueDates = new Set(logs.map(log =>
        new Date(log.logDate).toISOString().split('T')[0]
      ));
      return uniqueDates.size;
    } else {
      // For standard projects, each entry is one row
      return logs.length;
    }
  };

  // Calculate status counts based on rows, not individual entries
  const calculateStatusCount = (logs: any[], status: string) => {
    const statusLogs = logs.filter(log => log.verificationStatus === status);
    return calculateRowCount(statusLogs);
  };

  // Calculate data source counts based on rows, not individual entries
  const calculateDataSourceCount = (logs: any[], source: string) => {
    const sourceLogs = logs.filter(log => log.dataSource === source);
    return calculateRowCount(sourceLogs);
  };

  // Calculate total generation - for hybrid projects, only count outgoing data to avoid double counting
  const calculateTotalGeneration = (logs: any[]) => {
    const isHybridProject = project.type === "RENEWABLE_ENERGY" && project.metadata?.projectSubType === "HYBRID_RENEWABLE";

    if (isHybridProject) {
      // For hybrid projects, only sum outgoing generation (which is solar + wind combined)
      return logs
        .filter(log => log.metadata?.generationType === 'OUTGOING')
        .reduce((sum, log) => sum + (Number(log.quantity) || 0), 0);
    } else {
      // For standard projects, sum all entries
      return logs.reduce((sum, log) => sum + (Number(log.quantity) || 0), 0);
    }
  };

  // Calculate summary metrics
  const totalPowerGenerated = calculateTotalGeneration(unitLogs);
  const totalEmissionReduction = emissionCalculations.reduce((sum, calc) => sum + calc.emissionReduction, 0);
  const totalRows = calculateRowCount(unitLogs);
  const verifiedRows = calculateStatusCount(unitLogs, "VERIFIED");
  const pendingRows = calculateStatusCount(unitLogs, "PENDING");

  // Chart data
  const powerGenerationData = unitLogs.map(log => ({
    month: new Date(log.logDate).toLocaleDateString('en-US', { month: 'short', year: '2-digit' }),
    power: log.quantity / 1000, // Convert to MWh
    verified: log.verificationStatus === "VERIFIED"
  }));

  const dataSourceDistribution = [
    { name: "Manual Entry", value: calculateDataSourceCount(unitLogs, "MANUAL"), color: "#8884d8" },
    { name: "API Integration", value: calculateDataSourceCount(unitLogs, "API_INTEGRATION"), color: "#82ca9d" },
    { name: "CSV Upload", value: calculateDataSourceCount(unitLogs, "CSV_UPLOAD"), color: "#ffc658" }
  ];

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Project Monitoring</h1>
            <p className="text-muted-foreground">Loading monitoring data...</p>
          </div>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="h-4 w-20 bg-muted animate-pulse rounded" />
                <div className="h-4 w-4 bg-muted animate-pulse rounded" />
              </CardHeader>
              <CardContent>
                <div className="h-8 w-16 bg-muted animate-pulse rounded mb-2" />
                <div className="h-3 w-24 bg-muted animate-pulse rounded" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Project Monitoring</h1>
          <p className="text-muted-foreground">
            Monitor power generation and emission reductions for {project.name}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={refreshData}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Add Data
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Power Generated</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{(totalPowerGenerated / 1000).toFixed(1)} MWh</div>
            <p className="text-xs text-muted-foreground">
              +12% from last quarter
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Emission Reductions</CardTitle>
            <TrendingDown className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalEmissionReduction.toFixed(1)} tCO2e</div>
            <p className="text-xs text-muted-foreground">
              {((totalEmissionReduction / project.estimatedReductions) * 100).toFixed(1)}% of target
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Data Verification</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{verifiedRows}/{totalRows}</div>
            <p className="text-xs text-muted-foreground">
              {pendingRows} pending verification
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Baseline Status</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              <Badge variant={baselineConfig?.status === "APPROVED" ? "default" : "secondary"}>
                {baselineConfig?.status || "Not Set"}
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground">
              {baselineConfig?.gridEmissionFactor} tCO2/MWh
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
          <TabsTrigger value="data-entry">Data Entry</TabsTrigger>
          <TabsTrigger value="calculations">Calculations</TabsTrigger>
          <TabsTrigger value="integrations">Integrations</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Power Generation Summary - Real Data */}
          <PowerGenerationSummary
            projectId={projectId}
            projectType={project.type}
            projectSubType={project.metadata?.projectSubType}
            refreshTrigger={refreshTrigger}
            onAddData={() => setActiveTab("data-entry")}
            onViewDetails={() => router.push(`/dashboard/projects/${projectId}/monitoring/data-entry`)}
          />

          {/* Power Generation Chart - Real Data */}
          <PowerGenerationChart
            projectId={projectId}
            projectType={project.type}
            projectSubType={project.metadata?.projectSubType}
          />

          <div className="grid gap-4 md:grid-cols-2">
            {/* Power Generation Trend - Mock Data for Comparison */}
            <Card>
              <CardHeader>
                <CardTitle>Power Generation Trend (Mock Data)</CardTitle>
                <CardDescription>Monthly power generation over time - for comparison</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={powerGenerationData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value) => [`${value} MWh`, "Power Generated"]} />
                    <Area type="monotone" dataKey="power" stroke="#8884d8" fill="#8884d8" fillOpacity={0.6} />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Data Source Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Data Sources</CardTitle>
                <CardDescription>Distribution of data collection methods</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={dataSourceDistribution}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {dataSourceDistribution.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="monitoring" className="space-y-4">
          {/* Estimated Carbon Credits Card */}
          <EstimatedCarbonCredits
            projectId={projectId}
            refreshTrigger={refreshTrigger}
          />

          {/* Power Generation Summary - Real Data */}
          <PowerGenerationSummary
            projectId={projectId}
            projectType={project.type}
            projectSubType={project.metadata?.projectSubType}
            refreshTrigger={refreshTrigger}
            onAddData={() => setActiveTab("data-entry")}
            onViewDetails={() => router.push(`/dashboard/projects/${projectId}/monitoring/data-entry`)}
          />

          {/* Power Generation Chart - Real Data */}
          <PowerGenerationChart
            projectId={projectId}
            projectType={project.type}
            projectSubType={project.metadata?.projectSubType}
          />

          {/* Monitoring Data Table */}
          <div className="max-w-7xl mx-auto">
            <MonitoringDataTable
              projectId={projectId}
              projectType={project.type}
              projectSubType={project.metadata?.projectSubType}
              refreshTrigger={refreshTrigger}
              onDataChange={() => {
                // Refresh the dashboard data when monitoring data changes
                loadMonitoringData();
                setRefreshTrigger(prev => prev + 1);
              }}
            />
          </div>
        </TabsContent>

        <TabsContent value="data-entry" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <Card className="cursor-pointer hover:shadow-md transition-shadow">
              <CardHeader className="text-center">
                <Edit className="h-8 w-8 mx-auto mb-2 text-blue-600" />
                <CardTitle>Manual Entry</CardTitle>
                <CardDescription>Enter power generation data manually</CardDescription>
              </CardHeader>
              <CardContent className="text-center">
                <Button
                  className="w-full"
                  onClick={() => router.push(`/dashboard/projects/${projectId}/monitoring/data-entry?tab=manual`)}
                >
                  Start Manual Entry
                </Button>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:shadow-md transition-shadow">
              <CardHeader className="text-center">
                <FileSpreadsheet className="h-8 w-8 mx-auto mb-2 text-green-600" />
                <CardTitle>CSV Upload</CardTitle>
                <CardDescription>
                  {project.type === "RENEWABLE_ENERGY" && project.metadata?.projectSubType === "HYBRID_RENEWABLE"
                    ? "Upload hybrid renewable data (Solar, Wind, Outgoing)"
                    : "Upload bulk data from CSV files"
                  }
                </CardDescription>
              </CardHeader>
              <CardContent className="text-center">
                <Button
                  className="w-full"
                  variant="outline"
                  onClick={() => router.push(`/dashboard/projects/${projectId}/monitoring/data-entry?tab=csv`)}
                >
                  Upload CSV
                </Button>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:shadow-md transition-shadow">
              <CardHeader className="text-center">
                <Wifi className="h-8 w-8 mx-auto mb-2 text-purple-600" />
                <CardTitle>API Integration</CardTitle>
                <CardDescription>Configure automated data collection</CardDescription>
              </CardHeader>
              <CardContent className="text-center">
                <Button
                  className="w-full"
                  variant="outline"
                  onClick={() => router.push(`/dashboard/projects/${projectId}/monitoring/data-entry?tab=api`)}
                >
                  Setup API
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="calculations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Emission Calculations</CardTitle>
              <CardDescription>View and manage emission reduction calculations</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">Emission calculation components will be implemented here.</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="integrations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>API Integrations</CardTitle>
              <CardDescription>Manage external data source integrations</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">API integration management will be implemented here.</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Monitoring Reports</CardTitle>
              <CardDescription>Generate and download monitoring reports</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">Report generation features will be implemented here.</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
