"use client";

import { useState, useEffect } from "react";
import { format } from "date-fns";
import { 
  Shield, 
  Eye, 
  Check, 
  X, 
  Clock, 
  AlertCircle, 
  Building2, 
  FileText,
  Filter,
  Search,
  ChevronRight,
  Users
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { SPVVerificationStatus } from "@/types/spv";
import { toast } from "sonner";

interface SPVForVerification {
  id: string;
  name: string;
  verificationStatus: SPVVerificationStatus;
  createdAt: string;
  updatedAt: string;
  organization: {
    id: string;
    name: string;
    legalName: string | null;
    status: string;
    verificationStatus: string;
  };
  documents: Array<{
    id: string;
    documentType: string;
    fileName: string;
    verified: boolean;
    uploadedAt: string;
  }>;
  spvUsers: Array<{
    user: {
      id: string;
      name: string;
      email: string;
    };
  }>;
  _count: {
    projects: number;
    spvUsers: number;
  };
  verificationProgress: {
    hasBasicInfo: boolean;
    documentsUploaded: number;
    documentsVerified: number;
    requiredDocumentsUploaded: number;
    totalRequiredDocuments: number;
    completionPercentage: number;
  };
}

interface VerificationStats {
  pending: number;
  inReview: number;
  needsMoreInfo: number;
  verified: number;
  rejected: number;
  total: number;
}

export function SPVVerificationDashboard() {
  const [spvs, setSPVs] = useState<SPVForVerification[]>([]);
  const [stats, setStats] = useState<VerificationStats>({
    pending: 0,
    inReview: 0,
    needsMoreInfo: 0,
    verified: 0,
    rejected: 0,
    total: 0,
  });
  const [loading, setLoading] = useState(true);
  const [selectedSPV, setSelectedSPV] = useState<SPVForVerification | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  
  // Filters
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const fetchSPVs = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: "10",
      });
      
      if (statusFilter !== "all") {
        params.append("status", statusFilter);
      }

      const response = await fetch(`/api/admin/spv-verification?${params}`);
      if (!response.ok) {
        throw new Error("Failed to fetch SPVs");
      }

      const data = await response.json();
      setSPVs(data.spvs);
      setStats(data.statistics);
      setTotalPages(data.pagination.pages);
    } catch (error) {
      console.error("Error fetching SPVs:", error);
      toast.error("Failed to load SPV verification queue");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSPVs();
  }, [currentPage, statusFilter]);

  const getStatusColor = (status: SPVVerificationStatus) => {
    switch (status) {
      case "VERIFIED":
        return "bg-green-100 text-green-800 border-green-200";
      case "REJECTED":
        return "bg-red-100 text-red-800 border-red-200";
      case "IN_REVIEW":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "NEEDS_MORE_INFO":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "PENDING_VERIFICATION":
        return "bg-gray-100 text-gray-800 border-gray-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStatusIcon = (status: SPVVerificationStatus) => {
    switch (status) {
      case "VERIFIED":
        return <Check className="h-4 w-4" />;
      case "REJECTED":
        return <X className="h-4 w-4" />;
      case "IN_REVIEW":
        return <Eye className="h-4 w-4" />;
      case "NEEDS_MORE_INFO":
        return <AlertCircle className="h-4 w-4" />;
      case "PENDING_VERIFICATION":
        return <Clock className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const filteredSPVs = spvs.filter(spv => 
    spv.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    spv.organization.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const openSPVDetails = (spv: SPVForVerification) => {
    setSelectedSPV(spv);
    setDetailsOpen(true);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">SPV Verification Dashboard</h1>
          <p className="text-muted-foreground">
            Review and verify SPV applications
          </p>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-gray-600" />
              <div>
                <p className="text-2xl font-bold">{stats.pending}</p>
                <p className="text-xs text-muted-foreground">Pending</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Eye className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-2xl font-bold">{stats.inReview}</p>
                <p className="text-xs text-muted-foreground">In Review</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-yellow-600" />
              <div>
                <p className="text-2xl font-bold">{stats.needsMoreInfo}</p>
                <p className="text-xs text-muted-foreground">Needs Info</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Check className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-2xl font-bold">{stats.verified}</p>
                <p className="text-xs text-muted-foreground">Verified</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <X className="h-4 w-4 text-red-600" />
              <div>
                <p className="text-2xl font-bold">{stats.rejected}</p>
                <p className="text-xs text-muted-foreground">Rejected</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Shield className="h-4 w-4 text-purple-600" />
              <div>
                <p className="text-2xl font-bold">{stats.total}</p>
                <p className="text-xs text-muted-foreground">Total</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search SPVs or organizations..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-fit min-w-[160px] sm:min-w-[200px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="PENDING_VERIFICATION">Pending</SelectItem>
                <SelectItem value="IN_REVIEW">In Review</SelectItem>
                <SelectItem value="NEEDS_MORE_INFO">Needs More Info</SelectItem>
                <SelectItem value="VERIFIED">Verified</SelectItem>
                <SelectItem value="REJECTED">Rejected</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* SPV List */}
      <Card>
        <CardHeader>
          <CardTitle>SPV Applications</CardTitle>
          <CardDescription>
            {filteredSPVs.length} SPV{filteredSPVs.length !== 1 ? 's' : ''} found
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-20 bg-muted rounded-lg"></div>
                </div>
              ))}
            </div>
          ) : filteredSPVs.length === 0 ? (
            <div className="text-center py-8">
              <Shield className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <p className="text-muted-foreground">No SPVs found</p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredSPVs.map((spv) => (
                <div
                  key={spv.id}
                  className="flex items-center gap-4 p-4 border rounded-lg hover:bg-muted/50 cursor-pointer transition-colors"
                  onClick={() => openSPVDetails(spv)}
                >
                  <div className="flex-shrink-0">
                    <Building2 className="h-8 w-8 text-muted-foreground" />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-medium truncate">{spv.name}</h3>
                      <Badge className={`text-xs ${getStatusColor(spv.verificationStatus)}`}>
                        {getStatusIcon(spv.verificationStatus)}
                        <span className="ml-1">{spv.verificationStatus.replace(/_/g, ' ')}</span>
                      </Badge>
                    </div>
                    
                    <p className="text-sm text-muted-foreground mb-2">
                      {spv.organization.name}
                    </p>
                    
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <span className="flex items-center gap-1">
                        <FileText className="h-3 w-3" />
                        {spv.verificationProgress.documentsVerified}/{spv.verificationProgress.totalRequiredDocuments} docs verified
                      </span>
                      <span className="flex items-center gap-1">
                        <Users className="h-3 w-3" />
                        {spv._count.spvUsers} user{spv._count.spvUsers !== 1 ? 's' : ''}
                      </span>
                      <span>Updated {format(new Date(spv.updatedAt), 'MMM d, yyyy')}</span>
                    </div>
                    
                    <Progress 
                      value={spv.verificationProgress.completionPercentage} 
                      className="mt-2 h-1"
                    />
                  </div>
                  
                  <div className="flex-shrink-0">
                    <ChevronRight className="h-4 w-4 text-muted-foreground" />
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center gap-2">
          <Button
            variant="outline"
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          <span className="flex items-center px-4 text-sm text-muted-foreground">
            Page {currentPage} of {totalPages}
          </span>
          <Button
            variant="outline"
            onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      )}

      {/* SPV Details Dialog */}
      <Dialog open={detailsOpen} onOpenChange={setDetailsOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              {selectedSPV?.name}
            </DialogTitle>
            <DialogDescription>
              SPV verification details and actions
            </DialogDescription>
          </DialogHeader>
          
          {selectedSPV && (
            <div className="space-y-6">
              {/* Status and Progress */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Shield className="h-4 w-4" />
                      <span className="font-medium">Verification Status</span>
                    </div>
                    <Badge className={`${getStatusColor(selectedSPV.verificationStatus)}`}>
                      {getStatusIcon(selectedSPV.verificationStatus)}
                      <span className="ml-1">{selectedSPV.verificationStatus.replace(/_/g, ' ')}</span>
                    </Badge>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <FileText className="h-4 w-4" />
                      <span className="font-medium">Document Progress</span>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Verified Documents</span>
                        <span>{selectedSPV.verificationProgress.documentsVerified}/{selectedSPV.verificationProgress.totalRequiredDocuments}</span>
                      </div>
                      <Progress value={(selectedSPV.verificationProgress.documentsVerified / selectedSPV.verificationProgress.totalRequiredDocuments) * 100} />
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Organization Info */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Organization Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium">Organization Name</p>
                      <p className="text-sm text-muted-foreground">{selectedSPV.organization.name}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Legal Name</p>
                      <p className="text-sm text-muted-foreground">{selectedSPV.organization.legalName || 'Not provided'}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Organization Status</p>
                      <Badge variant="outline">{selectedSPV.organization.status}</Badge>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Verification Status</p>
                      <Badge variant="outline">{selectedSPV.organization.verificationStatus}</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Action Buttons */}
              <div className="flex justify-end gap-3">
                <Button variant="outline" onClick={() => setDetailsOpen(false)}>
                  Close
                </Button>
                <Button 
                  onClick={() => {
                    // Navigate to detailed SPV verification page
                    window.open(`/admin/spv-verification/${selectedSPV.id}`, '_blank');
                  }}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
