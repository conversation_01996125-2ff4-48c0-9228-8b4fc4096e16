"use client";

import { useState, useEffect } from "react";
import { format } from "date-fns";
import { 
  Shield, 
  Check, 
  X, 
  AlertCircle, 
  Building2, 
  FileText,
  User,
  CreditCard,
  MapPin,
  Calendar,
  Phone,
  Mail,
  Loader2
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { SPVDocumentViewer } from "@/components/spv/spv-document-viewer";
import { SPVVerificationStatus } from "@/types/spv";
import { toast } from "sonner";

interface SPVVerificationDetailsProps {
  spvId: string;
}

interface SPVDetails {
  id: string;
  name: string;
  purpose: string | null;
  jurisdiction: string | null;
  status: string;
  verificationStatus: SPVVerificationStatus;
  
  // Basic info
  country: string | null;
  legalStructure: string | null;
  registrationNumber: string | null;
  
  // Verification fields
  gstNumber: string | null;
  cinNumber: string | null;
  panNumber: string | null;
  incorporationDate: string | null;
  registeredAddress: string | null;
  contactPersonName: string | null;
  contactPersonEmail: string | null;
  contactPersonMobile: string | null;
  bankAccountNumber: string | null;
  ifscCode: string | null;
  
  // Metadata
  verificationNotes: string | null;
  verifiedBy: string | null;
  verifiedAt: string | null;
  rejectionReason: string | null;
  createdAt: string;
  updatedAt: string;
  
  organization: {
    id: string;
    name: string;
    legalName: string | null;
  };
  
  documents: Array<{
    id: string;
    documentType: string;
    fileName: string;
    fileUrl: string;
    fileSize: number | null;
    mimeType: string | null;
    uploadedAt: string;
    verified: boolean;
    verifiedAt: string | null;
    notes: string | null;
    uploader: {
      id: string;
      name: string;
      email: string;
    };
    verifier?: {
      id: string;
      name: string;
      email: string;
    };
  }>;
}

export function SPVVerificationDetails({ spvId }: SPVVerificationDetailsProps) {
  const [spv, setSPV] = useState<SPVDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionDialog, setActionDialog] = useState<{
    isOpen: boolean;
    action: 'APPROVE' | 'REJECT' | 'REQUEST_MORE_INFO' | null;
  }>({
    isOpen: false,
    action: null,
  });
  const [actionNotes, setActionNotes] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const fetchSPVDetails = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/organizations/spvs/${spvId}/verification`);
      if (!response.ok) {
        throw new Error("Failed to fetch SPV details");
      }
      const data = await response.json();
      setSPV(data.spv);
    } catch (error) {
      console.error("Error fetching SPV details:", error);
      toast.error("Failed to load SPV details");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSPVDetails();
  }, [spvId]);

  const handleVerificationAction = async (action: 'APPROVE' | 'REJECT' | 'REQUEST_MORE_INFO') => {
    if (!spv) return;

    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/organizations/spvs/${spvId}/verification/actions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action,
          notes: actionNotes || undefined,
          rejectionReason: action === 'REJECT' ? actionNotes : undefined,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to perform verification action");
      }

      const result = await response.json();
      
      setActionDialog({ isOpen: false, action: null });
      setActionNotes("");
      toast.success(result.message);
      
      // Refresh SPV details
      await fetchSPVDetails();
    } catch (error) {
      console.error("Verification action error:", error);
      toast.error("Failed to perform verification action");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDocumentVerification = async (documentId: string, verified: boolean, notes?: string) => {
    try {
      const response = await fetch(`/api/organizations/spvs/${spvId}/documents/${documentId}/verify`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          verified,
          notes,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to verify document");
      }

      const result = await response.json();
      toast.success(result.message);
      
      // Refresh SPV details
      await fetchSPVDetails();
    } catch (error) {
      console.error("Document verification error:", error);
      toast.error("Failed to verify document");
    }
  };

  const openActionDialog = (action: 'APPROVE' | 'REJECT' | 'REQUEST_MORE_INFO') => {
    setActionDialog({ isOpen: true, action });
    setActionNotes(spv?.verificationNotes || "");
  };

  const getStatusColor = (status: SPVVerificationStatus) => {
    switch (status) {
      case "VERIFIED":
        return "bg-green-100 text-green-800 border-green-200";
      case "REJECTED":
        return "bg-red-100 text-red-800 border-red-200";
      case "IN_REVIEW":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "NEEDS_MORE_INFO":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "PENDING_VERIFICATION":
        return "bg-gray-100 text-gray-800 border-gray-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!spv) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
        <p className="text-muted-foreground">SPV not found</p>
      </div>
    );
  }

  const canPerformActions = ["PENDING_VERIFICATION", "IN_REVIEW", "NEEDS_MORE_INFO"].includes(spv.verificationStatus);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <Building2 className="h-6 w-6" />
            {spv.name}
          </h1>
          <p className="text-muted-foreground">{spv.organization.name}</p>
        </div>
        <Badge className={`${getStatusColor(spv.verificationStatus)}`}>
          {spv.verificationStatus.replace(/_/g, ' ')}
        </Badge>
      </div>

      {/* Action Buttons */}
      {canPerformActions && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Verification Actions</CardTitle>
            <CardDescription>
              Review the SPV details and documents, then choose an action
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-3">
              <Button
                onClick={() => openActionDialog('APPROVE')}
                className="bg-green-600 hover:bg-green-700"
              >
                <Check className="h-4 w-4 mr-2" />
                Approve SPV
              </Button>
              <Button
                variant="outline"
                onClick={() => openActionDialog('REQUEST_MORE_INFO')}
                className="border-yellow-600 text-yellow-600 hover:bg-yellow-50"
              >
                <AlertCircle className="h-4 w-4 mr-2" />
                Request More Info
              </Button>
              <Button
                variant="outline"
                onClick={() => openActionDialog('REJECT')}
                className="border-red-600 text-red-600 hover:bg-red-50"
              >
                <X className="h-4 w-4 mr-2" />
                Reject SPV
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Verification History */}
      {(spv.verificationNotes || spv.rejectionReason || spv.verifiedAt) && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Verification History</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {spv.verifiedAt && (
              <div className="flex items-start gap-3 p-3 bg-green-50 rounded-lg">
                <Check className="h-5 w-5 text-green-600 mt-0.5" />
                <div>
                  <p className="font-medium text-green-800">SPV Verified</p>
                  <p className="text-sm text-green-600">
                    Verified on {format(new Date(spv.verifiedAt), 'MMM d, yyyy HH:mm')}
                  </p>
                </div>
              </div>
            )}
            
            {spv.rejectionReason && (
              <div className="flex items-start gap-3 p-3 bg-red-50 rounded-lg">
                <X className="h-5 w-5 text-red-600 mt-0.5" />
                <div>
                  <p className="font-medium text-red-800">SPV Rejected</p>
                  <p className="text-sm text-red-600">{spv.rejectionReason}</p>
                </div>
              </div>
            )}
            
            {spv.verificationNotes && (
              <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
                <FileText className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <p className="font-medium text-blue-800">Verification Notes</p>
                  <p className="text-sm text-blue-600">{spv.verificationNotes}</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* SPV Details Tabs */}
      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="basic">Basic Info</TabsTrigger>
          <TabsTrigger value="legal">Legal Details</TabsTrigger>
          <TabsTrigger value="contact">Contact Info</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
        </TabsList>

        {/* Basic Information */}
        <TabsContent value="basic" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium">SPV Name</Label>
                    <p className="text-sm text-muted-foreground">{spv.name}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Purpose</Label>
                    <p className="text-sm text-muted-foreground">{spv.purpose || 'Not provided'}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Country</Label>
                    <p className="text-sm text-muted-foreground">{spv.country || 'Not provided'}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Jurisdiction</Label>
                    <p className="text-sm text-muted-foreground">{spv.jurisdiction || 'Not provided'}</p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium">Legal Structure</Label>
                    <p className="text-sm text-muted-foreground">{spv.legalStructure || 'Not provided'}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Registration Number</Label>
                    <p className="text-sm text-muted-foreground">{spv.registrationNumber || 'Not provided'}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Created</Label>
                    <p className="text-sm text-muted-foreground">
                      {format(new Date(spv.createdAt), 'MMM d, yyyy HH:mm')}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Last Updated</Label>
                    <p className="text-sm text-muted-foreground">
                      {format(new Date(spv.updatedAt), 'MMM d, yyyy HH:mm')}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Legal Details */}
        <TabsContent value="legal" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Legal & Compliance Details
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium">PAN Number</Label>
                    <p className="text-sm text-muted-foreground font-mono">{spv.panNumber || 'Not provided'}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">GST Number</Label>
                    <p className="text-sm text-muted-foreground font-mono">{spv.gstNumber || 'Not provided'}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">CIN Number</Label>
                    <p className="text-sm text-muted-foreground font-mono">{spv.cinNumber || 'Not provided'}</p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium">Incorporation Date</Label>
                    <p className="text-sm text-muted-foreground">
                      {spv.incorporationDate ? format(new Date(spv.incorporationDate), 'MMM d, yyyy') : 'Not provided'}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Registered Address</Label>
                    <p className="text-sm text-muted-foreground">{spv.registeredAddress || 'Not provided'}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Contact Information */}
        <TabsContent value="contact" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Contact Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium">Contact Person Name</Label>
                    <p className="text-sm text-muted-foreground">{spv.contactPersonName || 'Not provided'}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Contact Email</Label>
                    <p className="text-sm text-muted-foreground">{spv.contactPersonEmail || 'Not provided'}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Contact Mobile</Label>
                    <p className="text-sm text-muted-foreground">{spv.contactPersonMobile || 'Not provided'}</p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium">Bank Account Number</Label>
                    <p className="text-sm text-muted-foreground font-mono">{spv.bankAccountNumber || 'Not provided'}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">IFSC Code</Label>
                    <p className="text-sm text-muted-foreground font-mono">{spv.ifscCode || 'Not provided'}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Documents */}
        <TabsContent value="documents" className="space-y-4">
          <SPVDocumentViewer
            documents={spv.documents}
            canVerify={true}
            onVerifyDocument={handleDocumentVerification}
          />
        </TabsContent>
      </Tabs>

      {/* Action Dialog */}
      <Dialog
        open={actionDialog.isOpen}
        onOpenChange={(open) => {
          if (!open) {
            setActionDialog({ isOpen: false, action: null });
            setActionNotes("");
          }
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {actionDialog.action === 'APPROVE' && 'Approve SPV'}
              {actionDialog.action === 'REJECT' && 'Reject SPV'}
              {actionDialog.action === 'REQUEST_MORE_INFO' && 'Request More Information'}
            </DialogTitle>
            <DialogDescription>
              {actionDialog.action === 'APPROVE' && 'Confirm that this SPV meets all verification requirements'}
              {actionDialog.action === 'REJECT' && 'Provide a reason for rejecting this SPV'}
              {actionDialog.action === 'REQUEST_MORE_INFO' && 'Specify what additional information is needed'}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="actionNotes">
                {actionDialog.action === 'APPROVE' && 'Approval Notes (Optional)'}
                {actionDialog.action === 'REJECT' && 'Rejection Reason *'}
                {actionDialog.action === 'REQUEST_MORE_INFO' && 'Information Required *'}
              </Label>
              <Textarea
                id="actionNotes"
                placeholder={
                  actionDialog.action === 'APPROVE'
                    ? "Add any notes about the approval..."
                    : actionDialog.action === 'REJECT'
                    ? "Explain why this SPV is being rejected..."
                    : "Specify what additional information or documents are needed..."
                }
                value={actionNotes}
                onChange={(e) => setActionNotes(e.target.value)}
                className="min-h-[100px]"
              />
            </div>

            <div className="flex justify-end gap-3">
              <Button
                variant="outline"
                onClick={() => setActionDialog({ isOpen: false, action: null })}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                variant={actionDialog.action === 'APPROVE' ? 'default' : 'destructive'}
                onClick={() => handleVerificationAction(actionDialog.action!)}
                disabled={isSubmitting || (actionDialog.action !== 'APPROVE' && !actionNotes.trim())}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    {actionDialog.action === 'APPROVE' && 'Approve SPV'}
                    {actionDialog.action === 'REJECT' && 'Reject SPV'}
                    {actionDialog.action === 'REQUEST_MORE_INFO' && 'Request Information'}
                  </>
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
